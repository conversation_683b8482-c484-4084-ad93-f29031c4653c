import os
import gc
import sys
import csv
import time
import traceback
import numpy as np
import pandas as pd
from tqdm import tqdm
from datetime import datetime
from alive_progress import alive_bar

import nandinfo as ndinfo
from nandinfo import *
from device.device_configure import *
from colorama import init, Fore, Back, Style

import warnings
warnings.filterwarnings('ignore')

def add_item(grp_cols, list_item):
    if list_item:
        for key_item in list_item:
            if key_item in grp_cols:
                grp_cols = grp_cols
            else:
                grp_cols.append(key_item)
    return grp_cols

def HitRate_Best(df, group, store_dir, suffix_name):
    try:
        # df = df.rename(columns={'TestStep': 'Condition'})

        List = group
        List = add_item(List, list_item)

        if 'DR'.upper() in test_for.upper():
            df = df[(df['Read']=='Read Retry') & (df['First_Read']=='20241028')]    #hang, df['First_Read']=='None' -> df['First_Read']=='20241028'
        else:
            df = df[(df['Read']=='Read Retry')]

        df = df.reset_index(drop=True)
        df_filter = df.groupby(List, as_index=False)

        items = range(len(df_filter))
        with tqdm(total=len(df_filter), desc=Fore.GREEN + '     [' + str(datetime.now()) + '] -> ' 'PID-' + str(os.getpid()) + '  HitRate_Best', colour='GREEN', ncols=120, position=0) as tbar:
        # with alive_bar(len(items), title= 'PID-' + str(os.getpid()) + '  HitRate_Best', force_tty=True) as bar:
            i=0
            for name, group in df_filter:
                df_RRT = pd.DataFrame([name])
                df_RRT.columns = List # 全部重命名 columns = new_columns，新列名的长度必须与旧列名一致
                df_RRT['Total_CWs'] = group['Count'].sum()
                if '@'in df.loc[0, 'Condition']:
                    group.loc[:, 'Condition'] = group['Condition'].apply(lambda x: x.split(' @')[1].split('_')[1])
                for item in group['Condition'].unique():
                    split = item
                    df_RRT['RR_Entry'] = split
                    df_RRT['Retry_Passed_CWs'] = group[group['Condition'] == item]['Count'].sum()
                    df_RRT['Retry_Passed_Rate'] = '{:0.3%}'.format(group[group['Condition'] == item]['Count'].sum()/group['Count'].sum())
                    df_RRT['Max_FBC'] = group[group['Condition'] == item]['FBC'].max()

                    if i == 0:
                        df_RRT.to_csv(store_dir + r'/HitRate_Best_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                    else:
                        df_RRT.to_csv(store_dir + r'/HitRate_Best_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                    i = i + 1
                tbar.update()
                # bar()
        # print()

    except Exception as ex:
        print('Error: ', traceback.format_exc())


## HitRate_200bits function just run one loop decode using all entries
def HitRate_200bits(df_default, df_retry, grp_cols, fbc_criterion, store_dir, suffix_name):
    try:

        group_list_1 = grp_cols # ['Type', 'Cycle', 'Level', test_stress]
        group_list_1 = add_item(group_list_1, list_item) # ['Type', 'Cycle', 'Level', test_stress] + list_item
        group_list_2 = [die_locator,'Block', 'WordLine'] + grp_cols # ['Type', 'Cycle', 'Level', test_stress] + ['Block', 'WordLine']
        group_list_2 = add_item(group_list_2, list_item) # ['Type', 'Cycle', 'Level', test_stress] + ['Block', 'WordLine'] + list_item
        grouped = df_default.groupby(group_list_1, as_index=False) #将df_default按group_list_1中的列进行分组

        with tqdm(total=len(grouped), desc=Fore.GREEN + '     [' + str(datetime.now()) + '] -> ' 'PID-' + str(os.getpid()) + '  HitRate_200bits', colour='GREEN', ncols=120, position=0) as tbar:
        # with alive_bar(len(items), title= 'PID-' + str(os.getpid()) + '  HitRate_200bits', force_tty=True) as bar:
            i=0
            for name_1, group_1 in grouped:
                #[die_locator, 'Cycle', 'Level', test_stress] + [Temp]
                # ********** Total Codewords and Failed Codewords by Default Decode with FBC<200bits ********** #
                df_RRT = pd.DataFrame([name_1])
                df_RRT.columns = group_list_1

                group_1['FBC'] = group_1['FBC'].astype(int)
                df_RRT['Total_CWs'] = group_1['FBC'].count()    #count()是计算group_1['FBC']中非空值的个数
                df_failed_cw = group_1[group_1['FBC']>=fbc_criterion].copy() #大于200的CW
                df_RRT['Default_Passed_CWs'] = int(df_RRT['Total_CWs'] - df_failed_cw['FBC'].count())
                df_RRT['Default_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT['Total_CWs'] - df_failed_cw['FBC'].count())/int(df_RRT['Total_CWs']))
                # df_RRT['Default_Fail_CWs'] = df_failed_cw['FBC'].count()

                # ********** Read Entry with FBC<200bits ********** #           
                if df_failed_cw.empty == False: #如果default decoding失败的codewords不为空
                    #['Filter'] = ['Cycle','Page','CW','Block','die_locator','Type',Stress_name,'Temp']
                    extract = df_retry[df_retry['Filter'].isin(df_failed_cw['Filter'])].copy() #在read_retry中筛选出default decoding失败的codewords  
                    hitcount, hiterror = dict(), dict()

                    if extract.empty:
                        continue    #hang, 如果extract为空，跳过当前循环。因为default的block比read_retry中的block多，所以有可能default中的block在read_retry中没有对应的数据

                    #hang, “ValueError: cannot convert float NaN to integer” when process some data, 
                    #在time0的时候没有收Read Retry的数据 
                    #或者 Default fail的block在read_retry中没有收对应的数据
                    #同时也会导致Retry_Passed_CWs的总和要比default read 的df_failed_cw小！！！
                    L = extract['Condition'].unique().tolist()  #extract['Condition']是read_retry中的RR entry
                    L.sort(key=lambda x: int(x.split('RR')[1])) #按RR entry的编号排序
                    Retry_CWs = int(extract['FBC'].count()/len(L))
                    for split in L:
                        hitcount[split], hiterror[split] = 0, 0

                    df_rate = extract.groupby(group_list_2, as_index=False)
                    for name_2, group in df_rate:
                        group['FBC'] = group['FBC'].astype(int)
                        for split in L:
                            if int(group[group['Condition']==split]['FBC'].max()) < fbc_criterion:
                                hitcount[split] += int(group[group['Condition']==split]['FBC'].count()) # sum of all codewords together if they have the same split
                                if int(group[group['Condition']==split]['FBC'].max()) > hiterror[split]:
                                    hiterror[split] = group[group['Condition']==split]['FBC'].max() # after this entry decode successfuly: max fbc of all codeword in specified split
                                break

                    total_value = 0
                    for key, value in hitcount.items():
                        if value!=0:
                            df_RRT['RR_Entry'] = key
                            df_RRT['Retry_Passed_CWs'] = value
                            df_RRT['Retry_Passed_Rate'] = '{:0.2%}'.format(int(value)/int(df_RRT['Total_CWs']))
                            df_RRT['Max_FBC'] = hiterror[key]

                            total_value += value
                            df_RRT['Total_Percent'] = '{:0.2%}'.format(total_value/Retry_CWs)
                            if i == 0:
                                # df_RRT.rename(columns = {0:'Cycle', 1:'Type', 2:'Level', 3:stress_name, 4:'Total_CWs', 5:'Default_Passed_Rate'}, inplace=True)
                                df_RRT.to_csv(store_dir + r'/HitRate_200bits_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                            else:
                                df_RRT.to_csv(store_dir + r'/HitRate_200bits_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                            i = i + 1
                else:
                    df_RRT['RR_Entry'] = ' '
                    df_RRT['Retry_Passed_CWs'] = ' '
                    df_RRT['Retry_Passed_Rate'] = ' ' 
                    df_RRT['Max_FBC'] = group_1['FBC'].max()
                    df_RRT['Total_Percent'] = '100.00%'
                    if i == 0:
                        df_RRT.to_csv(store_dir + r'/HitRate_200bits_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                    else:
                        df_RRT.to_csv(store_dir + r'/HitRate_200bits_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                    i = i + 1
                tbar.update()

    except Exception as ex:
        print('Error: ', traceback.format_exc())    


def Optimum_HitRate(df_default, df_retry, grp_cols, fbc_criterion, store_dir, suffix_name):
### 该方法认为最好的RR entry是那些default decoding失败的LBAs在hard decoding时，best RR entry能使这些LBAs的FBC和（sum）最小
    try:

        grouped = df_default.groupby(grp_cols, as_index=False)
        items = range(len(grouped))
        df_failed_cw = pd.DataFrame()
        column_len = 1
        column_name = grp_cols + ['Total_CWs', 'Default_Passed_Rate', 'Default_Fail_CWs']

        with tqdm(total=len(grouped), desc=Fore.GREEN + '     [' + str(datetime.now()) + '] -> ' 'PID-' + str(os.getpid()) + '  HitRate_Optimum', colour='GREEN', ncols=120, position=0) as tbar:
        # with alive_bar(len(items), title= 'PID-' + str(os.getpid()) + '  HitRate_Decode', force_tty=True) as bar:
            i = 0
            for name, group in grouped:
                df_RRT = pd.DataFrame([name])
                df_RRT.columns = grp_cols

                df_RRT['Total_CWs'] = group['FBC'].count()
                default_failed_cws = group[group['FBC'].astype(int)>=fbc_criterion]
                df_RRT['Default_Passed_CWs'] = int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())
                df_RRT['Default_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())/int(df_RRT['Total_CWs']))
                df_RRT['Default_Fail_CWs'] = default_failed_cws['FBC'].count()

                rate_count = 1
                max_fbc_limit = codeword * 8
                avg_fbc_limit = codeword * 8
                previous_best_entry, best_entry = ' ', ' '
                df_failed_cw_next = default_failed_cws.copy()
                while df_failed_cw_next.empty == False:
                    df_failed_cw = df_retry[df_retry['Filter'].isin(df_failed_cw_next['Filter'])].copy()

                    df_failed_cw['FBC'] = df_failed_cw['FBC'].astype(int)
                    total_fbc = df_failed_cw['FBC'].sum()
                    avg_fbc = total_fbc

                    ### ******************************************************************************************************************* ###
                    ### ********************************************* 最初方法, 稍加改动后目前也在用 ***************************************** ###
                    if 0:
                        best_entry_name = list()
                        best_entry_sum = list()
                        L = df_failed_cw['Condition'].unique().tolist()

                        L.sort(key = lambda x: int(x.split('RR')[1]))
                        # for step in L:
                        for step in ['RR13', 'RR20']:
                            best_entry_name.append(step)
                            best_entry_sum.append(df_failed_cw[df_failed_cw['Condition']==step]['FBC'].sum()) # calculate the sum of FBC per condition
                        min_index = best_entry_sum.index(min(best_entry_sum))

                        retry_count = str(rate_count) + 'th'
                        df_RRT[retry_count + '_' + 'RR_Entry'] = best_entry_name[min_index] # .split(' @')[1].split('_')[1]
                        
                        df_failed_cw = df_failed_cw[df_failed_cw['Condition']==best_entry_name[min_index]]

                    ### ********************************************************************************************* ###
                    ### ******************************Below: 第二种取best entry方法******************************** ###
                    ### 最好的RR entry: 使default decoding失败的所有LBAs中FBC最大值最小的RR
                    # for name, group_rrt in df_failed_cw.groupby('Condition'):
                    #     if group_rrt['FBC'].max() <= max_fbc_limit:
                    #         if group_rrt['FBC'].max() == max_fbc_limit and name != best_entry:
                    #             if group_rrt['FBC'].mean() < avg_fbc_limit:
                    #                 best_entry = name
                    #                 avg_fbc_limit = group_rrt['FBC'].mean()
                    #                 max_fbc_limit = group_rrt['FBC'].max()
                            
                    #         elif group_rrt['FBC'].max() < max_fbc_limit and name != best_entry:
                    #             avg_fbc_limit = group_rrt['FBC'].mean()
                    #             max_fbc_limit = group_rrt['FBC'].max()
                    #             best_entry = name

                            # print(name, best_entry, max_fbc_limit, group_rrt['FBC'].sum())

                    ### ********************************************************************************************* ###
                    ### ******************************Above: 第二种取best entry方法******************************** ###

                    else:
                        ### 最好的RR entry: default decoding失败的那些LBAs在hard decoding条件下使这些LBAs的FBC和（sum）最小的RR
                        for name, group_rrt in df_failed_cw.groupby('Condition'):
                            if group_rrt['FBC'].sum() == total_fbc:
                                if group_rrt['FBC'].mean() < avg_fbc:
                                    best_entry = name
                                    avg_fbc = group_rrt['FBC'].mean()
                                    total_fbc = group_rrt['FBC'].sum()
                            elif group_rrt['FBC'].sum() < total_fbc:
                                best_entry = name
                                avg_fbc = group_rrt['FBC'].mean()
                                total_fbc = group_rrt['FBC'].sum()

                        retry_count = str(rate_count) + 'th'
                        df_RRT[retry_count + '_' + 'RR_Entry'] = best_entry
                        df_failed_cw = df_failed_cw[df_failed_cw['Condition']==best_entry]

                    Total_CWs = df_failed_cw['FBC'].count()
                    df_failed_cw_next = df_failed_cw[df_failed_cw['FBC']>=fbc_criterion]
                    df_RRT[retry_count + '_' + 'Retry_Passed_CWs'] = Total_CWs - df_failed_cw_next['FBC'].count()

                    df_RRT[retry_count + '_' + 'Retry_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT[retry_count + '_' + 'Retry_Passed_CWs'])/int(df_RRT['Total_CWs']))
                    
                    if df_failed_cw_next.empty == True:
                        df_RRT['Max_FBC'] = df_failed_cw['FBC'].max() 

                    if df_RRT.shape[1] > column_len:
                        column_len = df_RRT.shape[1]
                        column_name = list(df_RRT)

                    if (Total_CWs - df_failed_cw_next['FBC'].count()) == 0:
                        break
                    if rate_count >= rrt_entries:
                        break
                    rate_count = rate_count + 1
                
                    tbar.update()

                if i == 0:
                    df_RRT.to_csv(store_dir + r'/HitRate_Optimum_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                else:
                    df_RRT.to_csv(store_dir + r'/HitRate_Optimum_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                i = i + 1
                # bar()         
        # print()

    except Exception as ex:
        print('Error: ', traceback.format_exc())


def Max_Ratio_HitRate(df_default, df_retry, grp_cols, fbc_criterion, store_dir, suffix_name):
### 该方法认为最好的RR entry是那些default decoding失败的LBAs hard decoding时，在满足FBC<200bits条件下best RR entry能使decoded LBA个数（LBA count数）最多
### Best Entry: when those default decoding failed LBAs do hard decoding，it thinks the best RR entry is that can decoded maximum these LBA count meeting the spec of FBC<200bits
    try:
        grouped = df_default.groupby(grp_cols, as_index=False)
        items = range(len(grouped))
        df_failed_cw = pd.DataFrame()
        column_len = 1
        column_name = grp_cols + ['Total_CWs', 'Default_Passed_Rate', 'Default_Fail_CWs']
        with tqdm(total=len(grouped), desc=Fore.GREEN + '     [' + str(datetime.now()) + '] -> ' 'PID-' + str(os.getpid()) + '  HitRate_Ratio', colour='GREEN', ncols=120, position=0) as tbar:
            i = 0
            for name, group in grouped:
                df_RRT = pd.DataFrame([name])
                df_RRT.columns = grp_cols

                df_RRT['Total_CWs'] = group['FBC'].count()
                # default_failed_cws = group[group['FBC'].astype(int)>=fbc_criterion] #找出FBC>=200的CW
                default_failed_cws = group[group['FBC']>=fbc_criterion] #找出FBC>=200的CW
                df_RRT['Default_Passed_CWs'] = int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())
                df_RRT['Default_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())/int(df_RRT['Total_CWs']))
                df_RRT['Default_Fail_CWs'] = default_failed_cws['FBC'].count()

                rate_count = 1
                best_entry = ' '
                df_failed_cw_next = default_failed_cws.copy()
                while df_failed_cw_next.empty == False:
                    df_failed_cw = df_retry[df_retry['Filter'].isin(df_failed_cw_next['Filter'])].copy() #在read_retry中筛选出default decoding失败的codewords

                    df_failed_cw['FBC'] = df_failed_cw['FBC'].astype(int)
                    total_cws = 0
                    avg_fbc = df_failed_cw['FBC'].sum() # sum of all FBC

                    for name, group_rrt in df_failed_cw.groupby('Condition'):   #按RR entry分组， name是RR entry
                        if group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count() == total_cws:
                            if group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean() < avg_fbc:   #当两个entry的FBC<200的CW个数相等，平均值小的那个entry为best entry
                                best_entry = name
                                avg_fbc = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean()
                                total_cws = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count()
                        elif group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count() > total_cws: #找出FBC<200的CW个数最多的RR entry
                            best_entry = name
                            avg_fbc = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean()
                            total_cws = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count()               

                    retry_count = str(rate_count) + 'th'
                    df_RRT[retry_count + '_' + 'RR_Entry'] = best_entry
                    df_failed_cw = df_failed_cw[df_failed_cw['Condition']==best_entry]
                    Total_CWs = df_failed_cw['FBC'].count()

                    df_failed_cw_next = df_failed_cw[df_failed_cw['FBC']>=fbc_criterion]
                    df_RRT[retry_count + '_' + 'Retry_Passed_CWs'] = Total_CWs - df_failed_cw_next['FBC'].count()

                    df_RRT[retry_count + '_' + 'Retry_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT[retry_count + '_' + 'Retry_Passed_CWs'])/int(df_RRT['Total_CWs']))
                    
                    if df_failed_cw_next.empty == True:
                        df_RRT['Max_FBC'] = df_failed_cw['FBC'].max() 

                    if df_RRT.shape[1] > column_len:
                        column_len = df_RRT.shape[1]
                        column_name = list(df_RRT)

                    if (Total_CWs - df_failed_cw_next['FBC'].count()) == 0:
                        break
                    if rate_count >= rrt_entries:
                        break
                    rate_count = rate_count + 1

                    tbar.update()

                if i == 0:
                    df_RRT.to_csv(store_dir + r'/HitRate_Ratio_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                else:
                    df_RRT.to_csv(store_dir + r'/HitRate_Ratio_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')

                i = i + 1

    except Exception as ex:
        print('Error: ', traceback.format_exc())


def SelectRRs_HitRate(df_default, df_retry, grp_cols, fbc_criterion, store_dir, suffix_name):
### 该方法认为最好的10个RR entries是那些default decoding失败的LBAs hard decoding时，在满足FBC<200bits条件下best RR entry能使decoded LBA个数（LBA count数）最多
### when those default decoding failed LBAs do hard decoding，it thinks the best RR entry is that can get maximum these LBA count meeting the spec of FBC<200bits
    try:
        # kai_data = False
        # if 'Drive' in platform:
        #     if not kai_data:
        #         df = df.rename(columns={'TestStep': 'Condition'})
        #         if '@'in df.loc[0, 'Condition']:        
        #           df.loc[:, 'Condition'] = df['Condition'].apply(lambda x: x.split(' @')[1].split('_')[1])

        # if not kai_data:
        #     if 'B47R'.upper() in device or 'Micron'.upper() in device:
        #         df = df[df['Cell'] == ndinfo.mode].copy()
        #     # df['Filter'] = df['Cycle'].map(str).str.cat([df['Level'].map(str), df['CW'].map(str), df['Block'].map(str), df['Layer'].map(str), df['WordLine'].map(str), df[die_locator].map(str), df['Type'].map(str), df[ndinfo.test_stress].map(str)], sep='_')
        #     df['Filter'] = df['Cycle'].map(str).str.cat([df['Page'].map(str), df['CW'].map(str), df['Block'].map(str), df[die_locator].map(str), df['Type'].map(str), df[ndinfo.test_stress].map(str)], sep='_')
        #     df_default = df[df['Read']=='Default Read'].copy()
        #     df_retry = df[df['Read']==retry_mode].copy()
        # else: # Kai's data
        #     df['Filter'] = df['Cycle'].map(str).str.cat([df['CW'].map(str), df['Block'].map(str), df['Page'].map(str), df['ChpDie'].map(str), df['PP'].map(str), df[ndinfo.test_stress].map(str)], sep='_')
        #     df_default = df[df['FirstRead']=='1st'].copy()
        #     df_retry = df[df['FirstRead']=='RR'].copy()

        grouped = df_default.groupby(grp_cols, as_index=False)
        df_failed_cw = pd.DataFrame()
        column_len = 1
        column_name = grp_cols + ['Total_CWs', 'Default_Passed_Rate', 'Default_Fail_CWs']
        select_RR_total = 10
        with tqdm(total=len(grouped), desc=Fore.GREEN + '     [' + str(datetime.now()) + '] -> ' 'PID-' + str(os.getpid()) + '  HitRate_SelectedRR', colour='GREEN', ncols=120, position=0) as tbar:
            i = 0
            for name, group in grouped:
                df_RRT = pd.DataFrame([name])
                df_RRT.columns = grp_cols
                default_failed_cws = group[(group['FBC'].astype(int)>=fbc_criterion)].copy()
                df_RRT['Total_CWs'] = group['FBC'].count()
                df_RRT['Default_Passed_CWs'] = int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())
                df_RRT['Default_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT['Total_CWs']-default_failed_cws['FBC'].count())/int(df_RRT['Total_CWs']))
                df_RRT['Default_Fail_CWs'] = default_failed_cws['FBC'].count()
                best_entry_list = []
                best_entry_list.append('Default')

                j = 0
                default_failed_cws_init = default_failed_cws.copy()
                for j in range(10):
                    if default_failed_cws.empty == False:
                        rate_count = 1
                        df_retry_temp = df_retry[~(df_retry['Condition'].isin(best_entry_list))].copy()
                        best_entry = ' '
                        df_failed_cw_next = default_failed_cws_init.copy()
                        while df_failed_cw_next.empty == False:
                            df_failed_cw = df_retry_temp[df_retry_temp['Filter'].isin(df_failed_cw_next['Filter'])].copy()
                            df_failed_cw['FBC'] = df_failed_cw['FBC'].astype(int)
                            total_cws = 0
                            avg_fbc = df_failed_cw['FBC'].sum()
                            for name, group_rrt in df_failed_cw.groupby('Condition'):
                                if group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count() == total_cws:
                                    if group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean() < avg_fbc:
                                        best_entry = name
                                        avg_fbc = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean()
                                        total_cws = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count()
                                elif group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count() > total_cws:
                                    best_entry = name
                                    avg_fbc = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].mean()
                                    total_cws = group_rrt['FBC'][group_rrt['FBC']<fbc_criterion].count()           

                            retry_count = str(rate_count) + 'th'
                            df_RRT[retry_count + '_' + 'RR_Entry'] = best_entry
                            df_failed_cw = df_failed_cw[df_failed_cw['Condition']==best_entry]
                            Total_CWs = df_failed_cw['FBC'].count()

                            df_failed_cw_next = df_failed_cw[df_failed_cw['FBC']>=fbc_criterion]
                            if rate_count == 1:
                                best_entry_list.append(best_entry)
                                set1_failed = df_failed_cw_next['Filter'].copy()
                                set2_failed = df_failed_cw.copy()

                            df_RRT[retry_count + '_' + 'Retry_Passed_CWs'] = Total_CWs - df_failed_cw_next['FBC'].count()
                            df_RRT[retry_count + '_' + 'Retry_Passed_Rate'] = '{:0.2%}'.format(int(df_RRT[retry_count + '_' + 'Retry_Passed_CWs'])/int(df_RRT['Total_CWs']))
                            
                            if df_failed_cw_next.empty == True:
                                df_RRT['Max_FBC'] = df_failed_cw['FBC'].max() 

                            if df_RRT.shape[1] > column_len:
                                column_len = df_RRT.shape[1]
                                column_name = list(df_RRT)
                            
                            if (Total_CWs - df_failed_cw_next['FBC'].count()) == 0:
                                break
                            if rate_count >= rrt_entries:
                                break
                            rate_count = rate_count + 1

                        if j == 0:
                            set1 = set(set1_failed)
                            df_RRT['BestRR_Failed_CW'] = 'Joint1stRR_Failed_CW: ' + str(len(set1))
                            # df_RRT['BestRR_Failed_CW'] = len(set1)
                        else:
                            set2 = set(set2_failed['Filter'][set2_failed['FBC']<fbc_criterion])
                            ## (set1 & set2),(set1 ^ set2),((set1|set2)-set2),((set1|set2)-set1)
                            set1 = ((set1|set2)-set2)
                            df_RRT['BestRR_Failed_CW'] = 'Joint1stRR_Failed_CW: ' + str(len(set1))
                            # df_RRT['BestRR_Failed_CW'] = len(set1)
                        j = j + 1

                        if default_failed_cws_init.empty == False:
                            if i == 0:
                                df_RRT.to_csv(store_dir + r'/HitRate_SelectedRR_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                            else:
                                df_RRT.to_csv(store_dir + r'/HitRate_SelectedRR_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                            i = i + 1
                    
                    if default_failed_cws_init.empty == True:
                        if i == 0:
                            df_RRT.to_csv(store_dir + r'/HitRate_SelectedRR_' + suffix_name + '.csv', index = False, header = 1, mode = 'w+')
                        else:
                            df_RRT.to_csv(store_dir + r'/HitRate_SelectedRR_' + suffix_name + '.csv', index = False, header = 0, mode = 'a+')
                        i = i + 1
                        break

                tbar.update()

    except Exception as ex:
        print('Error: ', traceback.format_exc())

def hit_rate(df_optimum_by_page, df_init_csv, store_dir, suffix_name):

    if best_hit_rate_flag:  
    ###Optimum_Read_by_page的每个page最好的RR进行统计个数和百分比, 并输出到"HitRate_Best_by_xxx.csv"          
        if df_optimum_by_page.empty == False:
            df = df_optimum_by_page.copy()
        else:
            file = r'Optimum_Read_by_page' + '_' + suffix_name + '.csv'
            file_path = os.path.join(store_dir, file)
            df = pd.read_csv(file_path, low_memory=False)
        
        if 'Partial+DR' == test_for:#hang, add to split inner/edge wordline
            for partial in df['Type'].unique():
                if '100%' not in partial:
                    maxWL = df[df['Type']==partial].loc[:,'WordLine'].max()
                    if 'X36070' in device:
                        df['WLType'] = df[df['Type']==partial].loc[:,'Condition'].apply(lambda x: 'InnerWL' if re.findall(r'\d+', x)[0] in (list(range(70,123)+list(range(282,441)))) else ('EdgeWL' if re.findall(r'\d+', x)[0] in range(123,282) else 'Others'))
                    else:
                        df['WLType'] = df[df['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'InnerWL' if x < (maxWL-wl_per_layer+1) else 'EdgeWL')
                else:
                    df['WLType'] = df[df['Type']==partial].loc[:,'Type'].apply(lambda x: 'Close' if x == '100% Partially Prog' else 'Error')
        elif 'SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
            SPRD_Aggressor_Layer = SPRD_Aggressor_WL//wl_per_layer   
            df['WLType'] = df.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1)|(x//wl_per_layer==SPRD_Aggressor_Layer+1) else 'Non-VictimWL')
            # df['WLType'] = df.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1) else 'Non-VictimWL')        
        elif 'Partial+SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
            for partial in df['Type'].unique(): 
                maxWL = df[df['Type']==partial].loc[:,'WordLine'].max()
                df['WLType'] = df[df['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'Victim_EdgeWL' if x>=(maxWL//wl_per_layer)*wl_per_layer else ('Aggressor_EdgeWL' if (x>(maxWL-wl_per_layer) and x<(maxWL//wl_per_layer)*wl_per_layer) else ('Victim_InnerWL' if ((x//wl_per_layer)==(maxWL//wl_per_layer -2)) else ('InnerWL' if (x//wl_per_layer<(maxWL//wl_per_layer -2)) else 'Aggressor_InnerWL'))))    
        
        stress_name = [col_name for col_name in df.columns.values.tolist() if ndinfo.test_stress in col_name][0]

        if split_WL_zone_flag==1:
            if 'X36070' in device:
                def determine_edgewl_rrgroup(x):
                    if 0<= x <=425:
                        return 'Group1'
                    elif 426<= x <=851:
                        return 'Group2'
                    elif 852<= x <=1409:
                        return 'Group3'
                    else:
                        return 'Group0'
                def determine_inner_rrgroup(x):
                    if 0<= x <=425:
                        return 'Group4'
                    elif 426<= x <=851:
                        return 'Group5'
                    elif 852<= x <=1133:
                        return 'Group6'
                    else:
                        return 'Group0'
            else:
                def determine_edgewl_rrgroup(x):
                    if 1580<= x <=1599:
                        return 'Group0'
                    elif 0<= x <=419:
                        return 'Group1'
                    elif 420<= x <=1099:
                        return 'Group2'
                    elif 1100<= x <=1369:
                        return 'Group3'
                    elif 1370<= x <=1499:
                        return 'Group4'
                    else:
                        return 'Group7'
                def determine_inner_rrgroup(x):
                    if 1500<= x <=1589:
                        return 'Group0'
                    elif 0<= x <=829:
                        return 'Group5'
                    else:
                        return 'Group6'
                
            if 'Close' not in df['WLType'].unique():  #一个folder里只有一个type
                # open block edge wl
                df.loc[df['WLType'] == 'EdgeWL','RRGroup'] = df.loc[df['WLType'] == 'EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                # open bllock inner wl
                df.loc[df['WLType'] == 'InnerWL','RRGroup'] = df.loc[df['WLType'] == 'InnerWL', 'WordLine'].apply(determine_inner_rrgroup)                    
            else:
                # close block
                df.loc[:,'RRGroup'] = 'Group0'
        
        #raw data 就是按照type,stress分folder的，hit rate是每个folder处理然后merge
        if split_WL_zone_flag==1:
            HitRate_Best(df, ['Cycle','Type','Level', stress_name,'RRGroup'], store_dir, 'by_cycle_stress_level_type_RRGroup' + '_' + suffix_name)
            HitRate_Best(df, ['Cycle','Type', stress_name,'RRGroup'], store_dir, 'by_cycle_type_stress_RRGroup_no_level' + '_' + suffix_name)
            HitRate_Best(df, ['Type' , stress_name,'RRGroup'], store_dir, 'by_type_stress_RRGroup_no_cycle_level' + '_' + suffix_name)
            if 'Partial' in test_for or 'SPRD' == test_for:        
                HitRate_Best(df, ['Cycle','Type','Level', stress_name, 'WLType'], store_dir, 'by_cycle_type_stress_level_WL_RRGroup' + '_' + suffix_name)
                HitRate_Best(df, ['Cycle','Type', stress_name, 'WLType'], store_dir, 'by_cycle_type_stress_WL_RRGroup_no_level' + '_' + suffix_name)
                HitRate_Best(df, ['Type',stress_name, 'WLType'], store_dir, 'by_type_stress_WL_RRGroup_no_cycle_level' + '_' + suffix_name)  
        else:                  
            #raw data 就是按照type,stress分folder的，hit rate是每个folder处理然后merge
            HitRate_Best(df, ['Cycle','Type','Level', stress_name], store_dir, 'by_cycle_stress_level_type' + '_' + suffix_name)
            HitRate_Best(df, ['Cycle','Type', stress_name], store_dir, 'by_cycle_type_stress_no_level' + '_' + suffix_name)
            HitRate_Best(df, ['Type' , stress_name], store_dir, 'by_type_stress_no_cycle_level' + '_' + suffix_name)
            if 'Partial' in test_for or 'SPRD' == test_for:        
                HitRate_Best(df, ['Cycle','Type','Level', stress_name, 'WLType'], store_dir, 'by_cycle_type_stress_level_WL' + '_' + suffix_name)
                HitRate_Best(df, ['Cycle','Type', stress_name, 'WLType'], store_dir, 'by_cycle_type_stress_WL_no_level' + '_' + suffix_name)
                HitRate_Best(df, ['Type',stress_name, 'WLType'], store_dir, 'by_type_stress_WL_no_cycle_level' + '_' + suffix_name)

        del df_optimum_by_page

    if criteria_hit_rate_flag or optimum_hit_rate_flag or maxratio_hit_rate_flag or selectRR_hit_rate_flag:

        if df_init_csv.empty == False:
            df = df_init_csv.copy()
            del df_init_csv #hang, free memory
        else:
            file = r'Initialization_data' + '_'  + suffix_name + '.csv'
            file_path = os.path.join(store_dir, file)
            df = pd.read_csv(file_path, low_memory=False)
        
        if read_retry_block_count == 0:
            df = df.loc[df['Block'] % 2 == 0] #hang, read retry only for even block
        elif read_retry_block_count == 1:
            df = df.loc[df['Block'] % 2 == 1] #hang, read retry only for odd block

        if 'B47R'.upper() in device or 'Micron'.upper() in device:
            df = df[df['Cell'] == ndinfo.mode].copy()
        
        if split_WL_zone_flag==1:
            def determine_edgewl_rrgroup(x):
                if 1580<= x <=1599:
                    return 'Group0'
                elif 0<= x <=419:
                    return 'Group1'
                elif 420<= x <=1099:
                    return 'Group2'
                elif 1100<= x <=1369:
                    return 'Group3'
                elif 1370<= x <=1499:
                    return 'Group4'
                else:
                    return 'Group7'
            def determine_inner_rrgroup(x):
                if 1500<= x <=1589:
                    return 'Group0'
                elif 0<= x <=829:
                    return 'Group5'
                else:
                    return 'Group6'

        if 'Partial+DR' == test_for:#hang, add to split inner/edge wordline
            for partial in df['Type'].unique():
                if '100%' not in partial:
                    maxWL = df[df['Type']==partial].loc[:,'WordLine'].max()
                    df['WLType'] = df[df['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'InnerWL' if x < (maxWL-wl_per_layer+1) else 'EdgeWL')
                else:
                    df['WLType'] = df[df['Type']==partial].loc[:,'Type'].apply(lambda x: 'Close' if x == '100% Partially Prog' else 'Error')
            if split_WL_zone_flag==1:
                if 'Close' not in df['WLType'].unique():  #一个folder里只有一个type
                    # open block edge wl
                    df.loc[df['WLType'] == 'EdgeWL','RRGroup'] = df.loc[df['WLType'] == 'EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                    # open bllock inner wl
                    df.loc[df['WLType'] == 'InnerWL','RRGroup'] = df.loc[df['WLType'] == 'InnerWL', 'WordLine'].apply(determine_inner_rrgroup)                    
                else:
                    # close block
                    df.loc[:,'RRGroup'] = 'Group0'
                print('WLtype:', df['WLType'].unique())
                print('RRGroup:', df['RRGroup'].unique())
        elif 'SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
            SPRD_Aggressor_Layer = SPRD_Aggressor_WL//wl_per_layer   
            df['WLType'] = df.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1)|(x//wl_per_layer==SPRD_Aggressor_Layer+1) else 'Non-VictimWL')
            # df['WLType'] = df.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1) else 'Non-VictimWL')
        elif 'Partial+SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
            for partial in df['Type'].unique(): 
                maxWL = df[df['Type']==partial].loc[:,'WordLine'].max()
                df['WLType'] = df[df['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'Victim_EdgeWL' if x>=(maxWL//wl_per_layer)*wl_per_layer else ('Aggressor_EdgeWL' if (x>(maxWL-wl_per_layer) and x<(maxWL//wl_per_layer)*wl_per_layer) else ('Victim_InnerWL' if ((x//wl_per_layer)==(maxWL//wl_per_layer -2)) else ('InnerWL' if (x//wl_per_layer<(maxWL//wl_per_layer -2)) else 'Aggressor_InnerWL'))))    
            if split_WL_zone_flag==1:
                # open block edge wl
                df.loc[df['WLType'] == 'Victim_EdgeWL','RRGroup'] = df.loc[df['WLType'] == 'Victim_EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                df.loc[df['WLType'] == 'Aggressor_EdgeWL','RRGroup'] = df.loc[df['WLType'] == 'Aggressor_EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                # open bllock inner wl
                df.loc[df['WLType'] == 'Victim_InnerWL','RRGroup'] = df.loc[df['WLType'] == 'Victim_InnerWL', 'WordLine'].apply(determine_inner_rrgroup)
                df.loc[df['WLType'] == 'InnerWL','RRGroup'] = df.loc[df['WLType'] == 'InnerWL', 'WordLine'].apply(determine_inner_rrgroup)
                df.loc[df['WLType'] == 'Aggressor_InnerWL','RRGroup'] = df.loc[df['WLType'] == 'Aggressor_InnerWL', 'WordLine'].apply(determine_inner_rrgroup)                    
                print('WLtype:', df['WLType'].unique())
                print('RRGroup:', df['RRGroup'].unique())

        stress_name = [col_name for col_name in df.columns.values.tolist() if ndinfo.test_stress in col_name][0]        
        df['Filter'] = df['Cycle'].map(str).str.cat([df['Page'].map(str), df['CW'].map(str), df['Block'].map(str), df[die_locator].map(str), df['Type'].map(str), df[stress_name].map(str)], sep='_')
        if list_item:
            for lst in list_item:
                df['Filter'] = df['Filter'].map(str).str.cat([df[lst].map(str)], sep='_')
        
        df_default = df[(df['Read']=='Default Read')].copy()
        # df_retry = df[(df['Read']==retry_mode)].copy()
        df_retry = df[(df['Read']=='Read Retry')].copy() #hang
        del df #hang, free memory

        df_retry = df_retry.reset_index(drop=True)
        if '@'in df_retry.loc[:, 'Condition']: #loc[0, 'Condition']
            df_retry.loc[:, 'Condition'] = df_retry['Condition'].apply(lambda x: x.split(' @')[1].split('_')[1])

        if criteria_hit_rate_flag:
            suffix_name_new = ndinfo.suf_name_200bits + '_' + suffix_name
            hit_list_200bts = ndinfo.group_list_for_200bits[:]
            hit_list_200bts.remove(test_stress)
            hit_list_200bts.append(stress_name)
            ###对default read>200bits的CW, 按照RR顺序算hitrate, 并输出到"HitRate_200bits_byxxx.csv"
            HitRate_200bits(df_default, df_retry, hit_list_200bts, ndinfo.fbc_criterion, store_dir, suffix_name_new)    #by_cycle_stress_level_type
            HitRate_200bits(df_default, df_retry,  ['Cycle', 'Type', stress_name], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_no_level' + '_' + suffix_name)
            HitRate_200bits(df_default, df_retry,  ['Type',stress_name], ndinfo.fbc_criterion, store_dir, 'by_type_stress_no_cycle_level' + '_' + suffix_name)
            if 'Partial' in test_for or 'SPRD' == test_for:
                HitRate_200bits(df_default, df_retry, hit_list_200bts+['WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_level_WL' + '_' + suffix_name)
                HitRate_200bits(df_default, df_retry, ['Cycle', 'Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_WL_no_level' + '_' + suffix_name)
                HitRate_200bits(df_default, df_retry,  ['Type',stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_type_stress_WL_no_cycle_level' + '_' + suffix_name)

        if optimum_hit_rate_flag:
            suffix_name_new = ndinfo.suf_name_decode + '_' + suffix_name
            hit_list_CW = ndinfo.group_list_for_CWs[:]
            hit_list_CW.remove(test_stress)
            hit_list_CW.append(stress_name)
            ### 该方法认为最好的RR entry是那些default decoding失败的LBAs在hard decoding时，best RR entry能使这些LBAs的sum(FBC)最小
            ### 输出到"HitRate_Optimum_byxxx.csv"
            Optimum_HitRate(df_default, df_retry, hit_list_CW, ndinfo.fbc_criterion, store_dir, suffix_name_new)    #by_cycle_stress_level_type
            Optimum_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_no_level' + '_' + suffix_name)
            Optimum_HitRate(df_default, df_retry, ['Type',stress_name], ndinfo.fbc_criterion, store_dir, 'by_type_stress_no_cycle_level' + '_' + suffix_name)
            if 'Partial' in test_for or 'SPRD' == test_for:
                Optimum_HitRate(df_default, df_retry, hit_list_CW+['WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_level_WL' + '_' + suffix_name)
                Optimum_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_WL_no_level' + '_' + suffix_name)
                Optimum_HitRate(df_default, df_retry, ['Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_type_stress_WL_no_cycle_level' + '_' + suffix_name)

        if maxratio_hit_rate_flag:
            suffix_name_new = ndinfo.suf_name_decode + '_' + suffix_name
            hit_list_CW = ndinfo.group_list_for_CWs[:]
            hit_list_CW.remove(test_stress)
            hit_list_CW.append(stress_name)
            ### 该方法认为最好的RR entry是那些default decoding失败的LBAs hard decoding时，在满足FBC<200bits条件下best RR entry能使decoded LBA个数（LBA count数）最多
            ### 输出到"HitRate_Ratio_byxxx.csv"
            if split_WL_zone_flag!=1:
                Max_Ratio_HitRate(df_default, df_retry, hit_list_CW, ndinfo.fbc_criterion, store_dir, suffix_name_new)  #by_cycle_stress_level_type
                Max_Ratio_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_no_level' + '_' + suffix_name)
                Max_Ratio_HitRate(df_default, df_retry, ['Type',stress_name], ndinfo.fbc_criterion, store_dir, 'by_type_stress_no_cycle_level' + '_' + suffix_name)           
            if 'Partial' in test_for or 'SPRD' == test_for:
                if split_WL_zone_flag==1:
                    Max_Ratio_HitRate(df_default, df_retry, hit_list_CW+['WLType','RRGroup'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_level_WL_RRGroup' + '_' + suffix_name)
                    Max_Ratio_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name, 'WLType','RRGroup'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_WL_RRGroup_no_level' + '_' + suffix_name)
                    Max_Ratio_HitRate(df_default, df_retry, ['Type', stress_name, 'WLType','RRGroup'], ndinfo.fbc_criterion, store_dir, 'by_type_stress_WL_RRGroup_no_cycle_level' + '_' + suffix_name)
                else:    
                    Max_Ratio_HitRate(df_default, df_retry, hit_list_CW+['WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_level_WL' + '_' + suffix_name)
                    Max_Ratio_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_WL_no_level' + '_' + suffix_name)
                    Max_Ratio_HitRate(df_default, df_retry, ['Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_type_stress_WL_no_cycle_level' + '_' + suffix_name)

        if selectRR_hit_rate_flag:
            suffix_name_new = ndinfo.suf_name_decode + '_' + suffix_name
            hit_list_CW = ndinfo.group_list_for_CWs[:]
            hit_list_CW.remove(test_stress)
            hit_list_CW.append(stress_name)
            ### 该方法认为最好的10个RR entries是那些default decoding失败的LBAs hard decoding时，在满足FBC<200bits条件下best RR entry能使decoded LBA个数（LBA count数）最多
            SelectRRs_HitRate(df_default, df_retry, hit_list_CW, ndinfo.fbc_criterion, store_dir, suffix_name_new)  #by_cycle_stress_level_type
            SelectRRs_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_no_level' + '_' + suffix_name)
            SelectRRs_HitRate(df_default, df_retry, ['Type',stress_name], ndinfo.fbc_criterion, store_dir, 'by_type_stress_no_cycle_level' + '_' + suffix_name)
            if 'Partial' in test_for or 'SPRD' == test_for:
                SelectRRs_HitRate(df_default, df_retry, hit_list_CW+['WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_level_WL' + '_' + suffix_name)
                SelectRRs_HitRate(df_default, df_retry, ['Cycle', 'Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_cycle_type_stress_WL_no_level' + '_' + suffix_name)
                SelectRRs_HitRate(df_default, df_retry, ['Type', stress_name, 'WLType'], ndinfo.fbc_criterion, store_dir, 'by_type_stress_WL_no_cycle_level' + '_' + suffix_name)

        # del df_init_csv #move to head
        gc.collect()
