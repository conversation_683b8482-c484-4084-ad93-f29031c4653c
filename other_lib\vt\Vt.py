import os
import re
import time
import traceback
import threading
import pandas as pd
from tqdm import tqdm
from pickle import NONE
from pyfiglet import Figlet
from datetime import datetime
import multiprocessing

from colorama import init, Fore, Back, Style
init(autoreset=True)

base_path = r'C:\Users\<USER>\Desktop\outComes'

step = 2
AL, AR=-80,31-step
BL, BR=-31,30-step
CL, CR=-30,30-step
DL, DR=-31,29-step
EL, ER=-30,33-step
FL, FR=-34,38-step
GL, GR=-38,100-step

# A_value, B_value, C_value, D_value, E_value, F_value, G_value= [], [], [], [], [], [], []
# Slice, Die, RDC, Block, Plane, Wordline, Cycle = [], [], [], [], [], [], []
# vt_comb = []
vt_re = r'Vt_SPRDWL(\d+)_rdc_(\d+)_slice_(\d+)_chp_die_(\d+)_block_num_(\d+)_plane_(\d+)_wl_(\d+)_statue_(\w)_pec_(\d+).txt'

for folder in os.listdir(base_path):
    if 'Vt_for' in folder:
        Slice, Die, RDC, Block, Plane, Wordline, Cycle = [], [], [], [], [], [], []
        vt_comb = []
        for txt_file in os.listdir(os.path.join(base_path, folder)):
            if re.search(vt_re, os.path.join(base_path, folder, txt_file)):
                value = []
                
                re_info = re.search(vt_re, os.path.join(base_path, folder, txt_file))
                
                rdc = int(re_info[2])
                slice_num = int(re_info[3])
                chp_die = int(re_info[4])
                block = int(re_info[5])
                plane = int(re_info[6])
                wordline = int(re_info[7])
                pec = int(re_info[9])

                df_raw = pd.read_csv(os.path.join(base_path, folder, txt_file), delimiter=" ")
                df_raw.drop(df_raw.columns[[0]], axis=1, inplace=True)
                df_raw = df_raw.dropna(axis=1, how='all')
                df_raw =  df_raw.rename(columns={'0':'DAC', re_info[5]: 'Count'})

                if 'A' == re_info[8]:
                    value = df_raw.loc[AL:AR,'Count']
                elif 'B' == re_info[8]:
                    value = df_raw.loc[BL:BR, 'Count']
                elif 'C' == re_info[8]:
                    value = df_raw.loc[CL:CR,'Count']
                elif 'D' == re_info[8]:
                    value = df_raw.loc[DL:DR,'Count']
                elif 'E' == re_info[8]:
                    value = df_raw.loc[EL:ER,'Count']
                elif 'F' == re_info[8]:
                    value = df_raw.loc[FL:FR,'Count']
                elif 'G' == re_info[8]:
                    value = df_raw.loc[GL:GR,'Count']

                vt_value = value.astype(int)
                Slice += ([slice_num] * len(vt_value))
                
                Die+=([chp_die] * len(vt_value))
                RDC+=([rdc] * len(vt_value))
                Block+=([block] * len(vt_value))
                Plane+=([plane] * len(vt_value))
                Wordline+=([wordline] * len(vt_value))
                Cycle+=([pec] * len(vt_value))
                vt_value = value.tolist()
                vt_comb+=vt_value

        data = {'Slice':Slice,'Die':Die,'RDC':RDC, 'Block':Block,'Plane':Plane,'Wordline':Wordline,'Cycle':Cycle,'Vt': vt_comb}
        df = pd.DataFrame(data, columns=['Slice','Die','RDC', 'Block','Plane','Wordline','Cycle','Vt'])
        df.to_csv(os.path.join(base_path, folder,  folder + '.csv'), index=False, header=True, mode='w+')

        

