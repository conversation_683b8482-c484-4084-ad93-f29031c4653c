import code
import os
import gc
import re
import sys
import math
import glob
import time
import shutil
import openpyxl
import traceback
import threading
import numpy as np
import pandas as pd
from tqdm import tqdm
from pickle import NONE
from imghdr import tests
from timeit import timeit
from typing import NewType
from pyfiglet import Figlet
from scipy.stats import norm
from functools import partial
from datetime import datetime
from concurrent import futures
from numba import njit, jit, prange
from scipy.optimize import curve_fit
from alive_progress import alive_bar
from multiprocessing.dummy import Pool as ThreadPool

from nandinfo import *
import nandinfo as ndinfo
from function.hit_rate import *
from device.device_configure import *
from colorama import init, Fore, Back, Style
init(autoreset=True)

threadLock = threading.Lock()


def reduce_memory(dataframe_file):
    converted_int = pd.DataFrame()
    df_int = dataframe_file.select_dtypes(include=['int64','uint32','uint8'])
    if df_int.empty == False:
        for col in df_int.columns:
            # converted_int.loc[:,col] = df_init_int[col].astype('category')
            converted_int.loc[:,col] = df_int[col].apply(pd.to_numeric, downcast='unsigned')

    converted_float = pd.DataFrame()
    df_float = dataframe_file.select_dtypes(include=['float64'])
    if df_float.empty == False:
        for col in df_float.columns:
            converted_float.loc[:,col] = df_float[col].apply(pd.to_numeric, downcast='float')

    converted_init = pd.DataFrame()
    converted_obj = pd.DataFrame()
    df_obj = dataframe_file.select_dtypes(include=['object'])
    if df_obj.empty == False:
        for col in df_obj.columns:
            converted_obj.loc[:,col] = df_obj[col].astype('category')

    converted_init = pd.concat([converted_int, converted_obj], axis=1, ignore_index=False)

    return converted_init

def move2txtfolder(path, txt_file_list):
    txt_path = path + '\\.txt'
    if not os.path.exists(txt_path):
        os.makedirs(txt_path)

    for file in txt_file_list:
        des_path = os.path.join(txt_path, os.path.basename(file))
        shutil.move(file, des_path)

def del_csv(store_dir):
    del_id = 0
    for csv_folder in os.listdir(store_dir):
        if os.path.join(store_dir, csv_folder).endswith('.csv') and csv_folder !='log.csv':
            if del_id == 0:
                print(Fore.BLUE + '   [' + str(datetime.now()) + '] Delete Old Merged Data...')
            file_path = os.path.join(store_dir, csv_folder)
            os.remove(file_path)
            del_id = 1

def pd_add_stress(df, base_path):
    test_item = df['SegmentName'].unique()
    if len(test_item):
        for category in test_item:
            print(Fore.GREEN + '     -> Filter Condition: {0}'.format(category))
            stress_amount = category.split('_')[-1] #从segment的最后一个字符串获得，与“_”前面的内容无关
            stress_name = ndinfo.test_stress.split('[')[0] #stress_name是在nandinfo.py里面决定的，不是按照segmentname来的

            if 'Partial' in ndinfo.test_for: #hang, add for Partially Program
                if 'X49060' in device:
                    if 'OpenBlk' in category:
                        prog_part = category.split('_')[-2]
                    else:    
                        prog_part = category.split('_')[1]
                elif 'X36070' in device:
                    prog_part = ''
                else:
                    prog_part = category.split('_')[1]
            else:
                prog_part = ''

            if 'RD' in test_for:
                a = []
                if 'Close' in stress_amount:
                    stress = stress_amount #hang   
                    stress_val = stress_amount #hang
                else:
                    a = re.findall("\d+\.?\d*", stress_amount)
                    stress_val = list(map(int, a))[0]
                    if count_unit not in stress_amount and (stress_val) % rate == 0:
                        stress = str(int(stress_val/rate)) + count_unit
                    elif count_unit not in stress_amount and (stress_val) % rate != 0:
                        stress = str((stress_val)/rate) + count_unit                        
                    elif count_unit in stress_amount:
                        stress = str(stress_val) + count_unit
                stress_temp = base_path.split('\\')[-1].split('_')[-1] #hang, add stress temp for RD
                    
            elif 'DR' in test_for:
                a = []
                if 'Close' in stress_amount:
                    stress = stress_amount #hang   
                    stress_val = stress_amount #hang
                else:                
                    a = re.findall("\d+\.?\d*", stress_amount)
                    stress_val = list(map(int, a))[0]
                    stress = category.split('_')[-1]

                #hang, add for different temperature
                b = []
                b = re.findall("\d+\.?\d*", category.split('_')[0])
                if len(b)==0:
                    stress_temp = '75C'
                else:
                    stress_temp = '{}C'.format(b[0])
         
            else:
                stress_val = category.split('_', 1)[-1]
                stress = category.split('_', 1)[-1]

            if 'SPRD' in test_for and 'DR' in test_for:
                if 'hr' in stress_amount:    
                    stress_name = 'DR'
                    stress_val = category.split('_')[-1]
                    stress = category.split('_')[-1]
                    b = []
                    b = re.findall("\d+\.?\d*", category.split('_')[0])
                    if len(b)==0:
                        stress_temp = '75C'
                    else:
                        stress_temp = '{}C'.format(b[0])
                else: 
                    a = []
                    stress_name = 'SPRD'
                    a = re.findall("\d+\.?\d*", stress_amount)
                    stress_val = list(map(int, a))[0]
                    if count_unit not in stress_amount and (stress_val) % rate == 0:
                        stress = str(int(stress_val/rate)) + count_unit
                    elif count_unit not in stress_amount and (stress_val) % rate != 0:
                        stress = str((stress_val)/rate) + count_unit    
                    elif count_unit in stress_amount:
                        stress = stress_amount
                    stress_val = stress #这里用带单位的数据
                    stress_temp = '55C'

            if 'Partial' in test_for: #hang, add for Partially Program
                if 'DR' in test_for:
                    if 'X36070' in device:
                        stress_cond =  stress_temp
                    else:
                        stress_cond =  prog_part + '_' + stress_temp
                else:
                    stress_cond = prog_part
                path = os.path.join(base_path, 'RawData','data_for_' + stress_cond + '_' + stress_name + '_' + stress)  #hang, split to stress_cond to ensure the memory is enough
            else:
                stress_cond = stress_name
                path = os.path.join(base_path, 'RawData','data_for_' + stress_cond + '_' + stress)  #hang, split to stress_cond to ensure the memory is enough

            # path = os.path.join(base_path, 'data_for_' + stress_name + '_' + stress)
            

            folder = os.path.exists(path)
            if not folder:
                os.makedirs(path)
                
            df_filter = df[(df['SegmentName'].str.contains(category))].copy()
            df_filter['TestTemp'] = stress_temp   #hang, add for seprate the data at 75C-24hr and 85C-24hr
            df_filter = df_filter.drop('Value', axis=1).join(df_filter['Value'].str.split('@', expand=True).stack().reset_index(level=1, drop=True).rename('Value'))
            if df_filter['Label'].isnull().all():
                df_filter['Label'] = 'Default Read'
            df_filter.loc[df_filter['Label']=='Default Read', 'Condition'] = '0x0'
            df_filter.dropna(axis=1, how='all', inplace=True)
            df_filter['CHP_CE_LUN'] = df_filter['Channel'].astype(int).astype(str) + '_' + df_filter['Ce'].astype(int).astype(str) + '_' + df_filter['Lun'].astype(int).astype(str) #hang, add asint to avoid error
            df_filter.drop(['Id', 'SegmentIndex', 'Function', 'Counter', 'Vcc', 'VccQ', 'Vpp', 'Channel', 'Ce', 'Lun', 'DataRate', 'Measurement'], axis=1, inplace=True)
            df_filter['Block'] = df_filter['Block'].astype(int)
            #df_filter['SPRD_Flag'] = df_filter['Block'].apply(lambda x: 'SPRD' if (x%4<2) else 'NonSPRD') #hang, add for experiment: SPRD mix with DR
            
            if os.path.exists(os.path.join(base_path, 'filter.csv')): # the 'filter.csv' is under nandinfo.path
                filter_csv = pd.read_csv(os.path.join(base_path, 'filter.csv'), low_memory=False)
                if filter_csv.empty == False:
                    print(Fore.GREEN + '     Filter Condition Trigger...')
                    filter_condition = True
                    num_rows = filter_csv.shape[0]
                    for row in range(num_rows):
                        for col_name in filter_csv.columns:
                            filter_condition = (df_filter[col_name]==filter_csv.loc[row, col_name]) & filter_condition
                    df_filter = df_filter[~filter_condition]
            else:
                # None
                for bad_block in bad_block_list:
                    df_filter = df_filter[~((df_filter['Block']==bad_block.split("&")[1]) & (df_filter['CHP_CE_LUN']==bad_block.split("&")[0]))]
            
            df_filter[ndinfo.test_stress] = stress_val
            # df_filter.to_csv(os.path.join(path, stress_name + '_' + stress + '_' + 'raw_data.csv'), index=False)
            tocsv_path = os.path.join(path, stress_cond + '_' + stress + '_' + 'raw_data.csv')
            df_filter.to_csv(tocsv_path, index=False, mode='a+', header = False if os.path.exists(tocsv_path) else True)   #hang

            folder_path = os.path.join(base_path)

    # return folder_path #hang, 这里报错

def find_folder(path):
    parent_path = path
    print('parent_path: {}'.format(parent_path)) #hang
    print(Fore.MAGENTA + '[' + str(datetime.now()) + ']  Search folders to Process')
    if 'NplusT' in platform:
        folder_exist = False
        for folder in os.listdir(parent_path):  #hang, add RawData folder
            if 'RawData' in folder:
                for subfolder in os.listdir(os.path.join(parent_path, folder)):
                    if folder_keyword in subfolder:
                        folder_exist = True
                        continue
            
        if folder_exist == True:
            print(Fore.YELLOW + '     Skip Extract from NplusT Raw Data due to folder existed')
        else:
            try: #hang, add try-except to continue when occur error
                for file_csv in os.listdir(path): #os.lstdir() 返回指定路径下的文件和文件夹列表
                    if 'csv' in file_csv and 'filter' not in file_csv: #对不是filter的csv进行处理
                        file = os.path.join(path, file_csv) #每个file的路径
                        # print(file)
                        print(Fore.GREEN + '     Extract Key Para. Data from NplusT CSV')
                        # df_init = pd.read_csv(file, low_memory=False)
                        df_init_chunks = pd.read_csv(file, low_memory=False, chunksize= 2000000) #hang, read cvs by chunks
                        for df_init in df_init_chunks:
                            df_init = df_init[~df_init['SegmentName'].str.contains('Vth_Read', na=False)]         #删除Vth_read data            
                            df_init = df_init[(df_init['Measurement']=='FAILS4CHUNK')].copy()
                            # parent_path = pd_add_stress(df_init, path) #path是原始数据所在的文件夹
                            pd_add_stress(df_init, path) #path是原始数据所在的文件夹
                            del df_init #hang, delete to free memory
            except Exception as ex:
                print(Fore.RED + 'Error: ', traceback.format_exc())

    folder_list = []
    for folder in os.listdir(os.path.join(parent_path,'RawData')): #遍历上面产生的文件夹 #hang, 上面将生成的raw_data.csv都添加到RawData文件夹下
        if folder_keyword in folder: 
            if skip_folder_flag and os.path.exists(os.path.join(check_path, folder)):
                folder_path = os.path.join(check_path, folder)
                if True in [ skip_keyword in file_name for file_name in os.listdir(folder_path)]:
                    continue
            folder_list.append(folder)
    print(Fore.GREEN + '     Folder_List for {0} Files: {1}'.format(len(folder_list), folder_list))

    return folder_list

def find_outcome_folder(path):  #hang, search folder in 'OutCome' folder so that 'RawData' folder can be zipped
    parent_path = path
    print('parent_path: {}'.format(parent_path)) #hang
    print(Fore.MAGENTA + '[' + str(datetime.now()) + ']  Search folders to Process')
    if 'NplusT' in platform:
        folder_exist = True
        init_file_exist = True
        folder_list = []
        for folder in os.listdir(parent_path):  #hang, parent_path下的所有文件夹
            if 'Outcome' in folder:
                for subfolder in os.listdir(os.path.join(parent_path, folder)): #hang, outcome下的所有文件夹
                    if folder_keyword in subfolder:
                        folder_exist = folder_exist & True
                        if True in [ 'Initialization' in file_name for file_name in os.listdir(os.path.join(parent_path, folder, subfolder))]:
                            init_file_exist = init_file_exist & True                        
                            folder_list.append(subfolder)
        
        if folder_exist == True and init_file_exist == True:
            print(Fore.YELLOW + '     All Initialization file exist in Outcome folder.')
        else:
            print(Fore.RED + '     Lack of Initialization file exist in Outcome folder.')
            sys.exit()

    print(Fore.GREEN + '     Folder_List for {0} Files: {1}'.format(len(folder_list), folder_list))

    return folder_list

# *****************************************************************************************************************************************************
def file_merge(store_dir, merge_file):

    merge_dict = {}
    for file in merge_file:
        for o_file in os.listdir(store_dir):
            if file in o_file and 'WL' not in o_file and 'trend' not in o_file and '.csv' in o_file:
                file_path = os.path.join(store_dir, o_file)
                merge_dict[file] = file_path    #hang, 如果有两个file有相同的key word, 会覆盖前面的file
            if file in o_file and 'WL' in o_file and 'trend' not in o_file and '.csv' in o_file:
                file_path = os.path.join(store_dir, o_file)
                merge_dict[file] = file_path    #hang, 如果有两个file有相同的key word, 会覆盖前面的file
    return merge_dict

def get_data(data, shift, block):
    page_list, cw_list, block_list = [np.uint16(x) for x in range(0)], [np.uint8(x) for x in range(0)], [np.uint16(x) for x in range(0)]

    for i in prange(len(data) >> shift): # i is page, page size
        """
        # 'shift = 3' -> For each page (total 1151 pages for WD BiCS4) from 2 planes, each page has 4 codewords  --> Total Pages = len(data)>>3; >>3=2^3 -> e.g., =2*4 
        # 'shift = 4' -> For each page (total 2112 pages for B47R) from 4 planes, each page has 4 codewords --> Total Pages = len(data)>>4; >>4=2^4 -> e.g., =4*4        
        """
        page_list += [i,i,i,i]*plane
        cw_list += [0,1,2,3]*plane
        block_pln = block

        for p in range(plane):
            block_list += [block_pln, block_pln, block_pln, block_pln]
            block_pln = int(block_pln)+1
            
    return page_list, cw_list, block_list

def func_block_list(block):
    # block_list = [np.uint16(x) for x in range(0)]
    block_list, plane_list = [], []
    block = int(block)
    for p in range(plane):
        block_list += [block, block, block, block]
        # block = int(block)+1
        plane_list += [p, p, p, p]
    return block_list, plane_list

def page_data(name, block):
    try:
        with open(name, 'r') as f:
            data=[]
            for line in f: # reduce memory consumption
                a = line.split(' ')
                if a[0]=='Data_end\n':
                    break
                if a[0]!= '\n' and a[0]!='brbypass' and a[0]!='brbypassp' and a[0]!='brdrbp' and a[0]!='Data_start\n' and a[0]!='setr':
                    data+=a   
            data.remove('\n')
            data=[np.uint32(a) for a in data]

            fbc = data
            page = np.uint16(np.arange(len(fbc)) // (Loffset * plane))
            cw = np.uint8(np.tile([0,1,2,3], int(len(fbc)/Loffset)))
            block_list, plane_list = func_block_list(block)
            block_list = np.tile(block_list, int(len(fbc)/(Loffset * plane)))
            plane_list = np.tile(plane_list, int(len(fbc)/(Loffset * plane)))
            
        return page, fbc, cw, block_list, plane_list
    
    except Exception as ex:
        print(Fore.RED + '{0} >>> Error: {1}'.format(name, traceback.format_exc()))

def page2wl(x):
    try:
        if x <4: # SLC
            return int(x)
        elif x >= 1048 and x <= 1063: # MLC
            return (x-1048)//2+352
        elif x >= 2108: # SLC
            return x-1400
        elif x > 1063 and x < 2108: # TLC
            return (x+16)//bits_per_cell
        else: # TLC
            return (x+8)//bits_per_cell

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def NAND_CFG(df, device, mode):
    try:
        if 'Micron' in device or 'B47R' in device:
            df['WordLine'] = df['Page'].apply(lambda x: page2wl(x))        
        else:
            df['WordLine'] = df['Page'].apply(lambda x: x//bits_per_cell)

        df['Layer'] = df['WordLine'].apply(lambda x: x // wl_per_layer)
        # df['String'] = df['WordLine'].apply(lambda x: int(x % wl_per_layer))
        return df

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def level_func(device, mode, x):
    if mode=='TLC':
        if ('Micron'.upper() in device.upper()) or ('B47R'.upper() in device.upper()):
            if x < 4 or x >= 2108:
                return 'LSB'
            elif x <= 1063 and x >= 1048:
                if x % 2==0:
                    return 'LSB'
                if x % 2==1:
                    return 'CSB'
            elif x % bits_per_cell == 0:
                return 'MSB'if x < 1064 else 'CSB'
            elif x % bits_per_cell == 1:
                return 'LSB'if x < 1064 else 'MSB'
            elif x % bits_per_cell == 2:
                return 'CSB'if x < 1064 else 'LSB' 
        else:
            if x % bits_per_cell == 0:
                return 'LSB'
            elif x % bits_per_cell == 1:
                return 'CSB'
            elif x % bits_per_cell == 2:    
                return 'MSB'
    elif mode == 'QLC': #change for X36070, hang
        if 'X36070' in device:
            if x >= 5520:   #SLC
                return 'LP'
            elif (18 <= x <= 2705):  #QLC
                return ['LP', 'MP', 'UP', 'XP'][(x-18) % 4]
            elif (2778 <= x <= 5465):  #QLC
                return ['LP', 'MP', 'UP', 'XP'][(x-2778) % 4]
            elif (0 <= x <= 17):   #TLC
                return ['LP', 'MP', 'UP'][x % 3]
            elif (2706 <= x <= 2777):   #TLC
                return ['LP', 'MP', 'UP'][(x-2706) % 3]
            elif (5466 <= x <= 5519):   #TLC
                return ['LP', 'MP', 'UP'][(x-5466) % 3]
        else:
            levels = ['LP', 'MP', 'UP', 'XP']
            return levels[x % bits_per_cell]
    else:
        return 'LSB'
    
def mode_flag(device, mode, x):
    if mode == 'TLC':
        if ('Micron'.upper() in device.upper()) or ('B47R'.upper() in device.upper()):
            if x < 4 or x >= 2108:
                return 'SLC'
            elif x <= 1063 and x >= 1048:
                return 'MLC'
            else:
                return 'TLC' 
        else:
            return 'TLC'
    else:
        return 'SLC'
    
def del_item(df, grp_cols, stress_name=''):
    if len(df['Block'].unique()) == 1:
            grp_cols.remove('Block')
    if len(df['Type'].unique()) == 1:
        grp_cols.remove('Type')
    if len(stress_name):
        if len(df[stress_name].unique()) == 1:
            grp_cols.remove(stress_name)
    if len(df[die_locator].unique()) == 1:
        grp_cols.remove(die_locator)

    return grp_cols

def add_item(grp_cols, list_item):
    if list_item:
        for key_item in list_item:
            if key_item in grp_cols:
                grp_cols = grp_cols
            else:
                grp_cols.append(key_item)
    return grp_cols

def get_block_value(base_block, increment):

    inc_blk = []
    for pec_key in base_block.keys():
        if isinstance(base_block[pec_key], list):
            for blk in base_block[pec_key]:
                inc_blk.append(blk + increment)
        else:
            inc_blk.append(base_block[pec_key] + increment)

    return inc_blk


# *************************** Below Merge All Testing Data into One CSV *************************** #
def txt2csv_merge(tbar, test_for, df_init_list, df_blk_list, pids_list, txt_file):
    try:
        txt = os.path.splitext(os.path.basename(txt_file))[0]
        if 'slice' in os.path.basename(txt_file):
            if (txt.split('slice')[1]).startswith('_'): # slice_0 
                slice = (os.path.splitext(os.path.basename(txt_file))[0].split('slice_')[1]).split('_')[0]
            else: # or slice0_ 
                slice = (os.path.splitext(os.path.basename(txt_file))[0].split('slice')[1]).split('_')[0]
        else:
            slice = 'NA'
            
        if 'die' in os.path.basename(txt_file):
            if (txt.split('die')[1]).startswith('_'): # die_0 
                die = (os.path.splitext(os.path.basename(txt_file))[0].split('die_')[1]).split('_')[0]
            else:
                die = (os.path.splitext(os.path.basename(txt_file))[0].split('die')[1]).split('_')[0]
        else: # or die0_
            die = 'NA'
            
        if 'block_num_' in os.path.basename(txt_file):
            block = (txt.split('block_num_')[1]).split('_')[0]
            # block = int(block) * plane
        else:
            tqdm.write(Fore.RED + "File '{0}', Line {1}: No keyword 'block' in filename".format(sys._getframe().f_code.co_filename, sys._getframe().f_lineno-1))
            return

        cycle = (os.path.splitext(os.path.basename(txt_file))[0].split('pec_')[1])
        
        # ************************ Below: Partially Program Setting ************************
        if 'Partial' in test_for and ('Sanity'.upper() in test_for.upper() or 'DR'.upper() in test_for.upper()):         
            open_block = [(get_block_value(base_blk, blk_freq * ii)) for ii in range(test_num_blk_per_pec)] # get all open blocks:[[], [],...]
            # print('open blk: ', open_block)
            
            for i_blk in range(len(open_block)): # open_block:[[], [],...]
                if int((os.path.basename(txt_file).split('block_num_')[1]).split('_')[0]) in open_block[i_blk]:
                    die = int((os.path.basename(txt_file).split('chp_die_')[1]).split('_')[0])
                    Die = 'Die' + str(die)
                    if 'All' in partial_prog_ratio.keys():
                        Partail_Ratio = partial_prog_ratio['All'][i_blk]
                    else:
                        Partail_Ratio = partial_prog_ratio[Die][i_blk]
                    Wordline = partial_prog_wl[Partail_Ratio]-1

            Prog = ' Partially Prog'

        elif 'Partial' in test_for and ('BlkRD' in test_for or 'SPRD' in test_for):
            choose_wl = int((os.path.basename(txt_file).split('WL')[1]).split('_')[0]) # for SPRD: = aggressor wl; for BlkRD: = RD wordline from 0 to this WL-1
            loc = aggressor_wl.index(choose_wl)
            Partail_Ratio = partial_prog_ratio[loc]
            Wordline = partial_prog_wl[Partail_Ratio]-1
            Prog = ' Partially Prog'

        else:
            Partail_Ratio = ''
            Prog = 'Fully Prog'
        
        Type = Partail_Ratio + Prog
        stress_value = 0
        # ********************************************************************************************************

        condition = txt
        if os.path.basename(txt_file)[0:2]=='RR':
            read = retry_mode
            ReadMode = 'RR'
            RR_Num = condition.split('_')[1]
        else:
            read = 'Default Read'
            ReadMode = 'NR'
            RR_Num = ''

        # ************************************ Below: Sanity Setting ************************************
        if 'Sanity' in test_for:
            condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'Sanity'
            stress_name = ndinfo.test_stress
            stress_value = 0

        # ************************************ Below: Retention Setting ************************************
        if 'DR' in test_for:
            if 'day'.upper() in condition.upper():
                time_unit = 'days'
            elif 'hr'.upper() in condition.upper() or 'hour'.upper() in condition.upper():
                time_unit = 'hrs'
            elif 'min'.upper() in condition.upper() or 'minute'.upper() in condition.upper():
                time_unit = 'mins'
            elif 'sanity' in condition or 'before_htdr_data' in condition:
                time_unit = 'hrs'
            else:
                time_unit = ''
                # print(" DR Test Error! - Line {0}-{1}: No correct time_unit in filename".format(sys._getframe().f_lineno-7, sys._getframe().f_lineno-2))
                # return

            if 'C_' in os.path.basename(txt_file):
                temp = condition.split('C_')[0].split('_')[-1] + 'C'
            else:
                if 'rtdr' in os.path.basename(txt_file):
                    temp = '25C'
                elif 'stdr' in os.path.basename(txt_file):
                    temp = '75C'
                elif 'ltdr' in os.path.basename(txt_file):
                    temp = '85C'
                else:
                    temp = '85C'

            if ('RR' not in condition) and ('2nd' not in condition):
                first_read = '1st Read'
            elif '2nd' in condition or ('RR_0_' in condition):
                first_read = '2nd Read'
            else:
                first_read = 'None'

            if 'sanity' in condition or 'before_htdr_data' in condition:
                stess_time = '0'
            
            elif (('retention_data' in condition) or ('dr_' in condition)) and ('close_blk_data' not in condition and 'blk_close' not in condition):
                stess_time = re.split('mins|hrs|hours|days', condition)[0].split('_')[-1]

            elif 'close_blk_data' in condition or 'blk_close' in condition:
                # stess_time = '110'
                stess_time = re.split('mins|hrs|hours|days', condition)[0].split('_')[-1]
                Wordline = 'Max_WL'
                Type = 'Close ' + Partail_Ratio + ' Partial Block'

            if 0:
                condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'DR @' + temp
            else:
                condition = ReadMode + RR_Num

            # # After creating item "condition", to get temperature and stress value 
            # pd_temp = (condition.split('@')[1]).split('_')[0]
            # read_temp = (condition.split('@')[2]).split('_')[0]

            if time_unit:
                stress_name = ndinfo.test_stress.split('[')[0] + '[' + time_unit + ']'
            else:
                stress_name = ndinfo.test_stress

            stress_value = stess_time

        else:
            first_read = 'None'

        # ************************************ BelowL: Read Disturb Setting ************************************
        if 'SPRD' in test_for:
            
            condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'SPRD @25C'
            if 'sanity' in condition or 'before_sprd_data' in condition:
                stress_rdc = 0

            elif 'close_blk_data' in condition or 'blk_close'  in condition:
                Wordline = 'Max_WL'
                Type = 'Close ' + Partail_Ratio + ' Partial Block'

                open_block = [(get_block_value(base_blk, blk_freq * ii)) for ii in range(len(rd_count_set))] # open_block = [[]]
                test_open_blk = int(os.path.splitext(txt_file)[0].split('_')[-3])
                for b in range(len(open_block)):
                    if test_open_blk in open_block[b]:
                        location = open_block[b].index(test_open_blk)
                        if count_unit not in rd_count_set[b][location] or count_unit=='1' or count_unit==1:
                            stress_rdc = float(rd_count_set[b][location])/rate
                            if float(rd_count_set[b][location]) % rate == 0:
                                stress_rdc = int(stress_rdc)
                        else:
                            stress_rdc = int(rd_count_set[b][location].split(str(count_unit))[0])

            elif 'rdc_' in condition:
                Type = Partail_Ratio + Prog
                if 'Level_0' in condition:
                        rd_page = 'on LSB'
                elif 'Level_1' in condition:
                    rd_page = 'on CSB'
                elif 'Level_2' in condition:
                    rd_page = 'on MSB'

                rd = (condition.split('rdc_')[1]).split('_')[0] # extract the read disturb count: whether include the count_unit?
                if count_unit not in condition or count_unit=='1' or count_unit==1:
                    stress_rdc = float(rd)/rate
                    if float(rd) % rate == 0:
                        stress_rdc = int(float(rd)/rate)
                else:
                    rd = (condition.split('rdc_')[1]).split('_')[0].split(str(count_unit))[0]
                    if float(rd) % 1 == 0:
                        stress_rdc = int(rd)
                    else:
                        stress_rdc = float(rd)
                condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'SPRD ' + rd_page +  ' @25C'

            stress_name = ndinfo.test_stress
            stress_value = stress_rdc

        # ************************************ BelowL: Read Disturb Setting ************************************
        if 'BlkRD' in test_for:
            
            if 'sanity' in condition or 'before_blkrd_data' in condition:
                stress_rdc = 0

            elif 'close_blk_data' in condition or 'blk_close'  in condition: 
                Wordline = 'Max_WL'
                Type = 'Close ' + Partail_Ratio + ' Partial Block'
                
                open_block = [(get_block_value(base_blk, blk_freq * ii)) for ii in range(len(rd_count_set))] # open_block = [[]]
                test_open_blk = int(os.path.splitext(txt_file)[0].split('_')[-3])
                for b in range(len(open_block)):
                    if test_open_blk in open_block[b]:
                        location = open_block[b].index(test_open_blk)
                        if count_unit not in rd_count_set[b][location] or count_unit=='1' or count_unit==1:
                            stress_rdc = float(rd_count_set[b][location])/rate
                            if float(rd_count_set[b][location]) % rate == 0:
                                stress_rdc = int(stress_rdc)
                        else:
                            stress_rdc = int(rd_count_set[b][location].split(str(count_unit))[0])

            elif 'rdc' in condition:         
                rd = (condition.split('rdc_')[1]).split('_')[0]
                Type = Partail_Ratio + Prog
                if count_unit not in condition or count_unit=='1' or count_unit==1:
                    if float(rd) % rate == 0:
                        stress_rdc = int(float(rd)/rate)
                    else:
                        stress_rdc = float(rd)/rate
                else:
                    rd = (condition.split('rdc_')[1]).split('_')[0].split(str(count_unit))[0]
                    if float(rd) % 1 == 0:
                        stress_rdc = int(rd)
                    else:
                        stress_rdc = float(rd)
                    # condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'BlkRD @25C'
            
            condition = 'E/P @25C_' + ReadMode + RR_Num + ' @25C' + '_' + 'BlkRD @25C'

            # # After creating item "condition", to get temperature and stress value
            # pd_temp = (condition.split('@')[1]).split('_')[0]
            # read_temp = (condition.split('@')[2]).split('_')[0]

            stress_name = ndinfo.test_stress
            stress_value = stress_rdc

        page, fbc, cw, block_list, plane_list = page_data(txt_file, block)
        df_init = pd.DataFrame(data = {'FBC': fbc, 'Page': page, 'Cycle': cycle, 'CW': cw, 'Block': block_list, 'Plane': plane_list}, columns=['FBC', 'Page', 'Cycle', 'CW', 'Block', 'Plane'])
        
    # *************************** CSV Data: Columns *************************** #  
        df_init['Level'] = df_init['Page'].apply(lambda x: level_func(device, mode, x))
        df_init = NAND_CFG(df_init, device, mode)
        df_init['Condition'] = condition
        df_init['Type'] = Type
        df_init['Read'] = read
        df_init['Slice_Die'] = str(slice) + '_' + str(die)
        df_init[stress_name] = stress_value
        # df_init['PD_temp_Read_temp'] = pd_temp + '_' + read_temp
        
        grp_cols = ['Cycle','Block','Level','Condition','Type','Read', stress_name] + [die_locator]
        grp_cols = add_item(grp_cols, list_item)
        
        if 'Partial' in test_for and Wordline != 'Max_WL':
            df_init = df_init[(df_init['WordLine'] <= Wordline)]
        if 'DR'.upper() in test_for.upper():
            df_init['First_Read'] = first_read
            grp_cols.extend(['First_Read']) 
        if 'B47R'.upper() in device or 'Micron'.upper() in device:
            df_init['Cell'] = df_init['Page'].apply(lambda x: mode_flag(device, mode, x))
            df_init = df_init[df_init['Cell'] == mode]

    # ### *************************** Start: Block files: Get BER  *************************** ###
        # df_block = df_init[['FBC'] + grp_cols].value_counts().reset_index(name='Count').sort_values('FBC', ascending=0) # for value_counts, it just output the grp_cols columns and remove other columns
        # df_block['BER'] = df_block['FBC']/(codeword*8)
        
    # ### *************************** Start: Block files: Get SFR and Merge RR based on block *************************** ###
    #     # FBC_col_list = ['FBC'] + grp_cols + ['BER']
    #     # df_merge = pd.DataFrame()
    #     # grouped = df_block.groupby(grp_cols)
    #     # for name, group in grouped:
    #     #     group_sum = group.groupby(FBC_col_list, as_index=False).sum().sort_values(by=['FBC'], ascending=0)
    #     #     group_sum['cum_Count'] = group_sum['Count'].cumsum()
    #     #     group_sum['SFR'] = group_sum['cum_Count']/group_sum['cum_Count'].max()
    #     #     df_merge = pd.concat([df_merge, group_sum], ignore_index=True)
    #     # df_merge.drop(['cum_Count'], axis=1, inplace=True)   

        if pids_list[-1] == str(os.getpid()):
            tbar.update()

        threadLock.acquire()
        try:
            df_init_list.append(df_init)
            # df_blk_list.append(df_block)
        finally:
            threadLock.release()


    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())   
# *************************** End: Initialization and Get SFR and Merge RR based on block *************************** #
        
def reduce_mem_usage(df):
    starttime = time.time()
    numerics = ['int16', 'int32', 'int64', 'float16', 'float32', 'float64']
    start_mem = df.memory_usage().sum() / 1024**2
    for col in df.columns:
        col_type = df[col].dtypes
        if col_type in numerics:
            c_min = df[col].min()
            c_max = df[col].max()
            if pd.isnull(c_min) or pd.isnull(c_max):
                continue
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
                elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                    df[col] = df[col].astype(np.int64)
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
                else:
                    df[col] = df[col].astype(np.float64)
    end_mem = df.memory_usage().sum() / 1024**2
    # print('    [INFO] >>> PID-' + str(os.getpid()) + ' -> Mem. usage decreased to {:5.2f} Mb ({:.1f}% reduction), time spend:{:2.2f} mins'.format(end_mem,
    #       100*(start_mem-end_mem)/start_mem, (time.time()-starttime)/60))
    return df

def df_block_level(df_init):
    grp_cols = ['Cycle','Block','Level','Condition','Type','Read', test_stress] + [die_locator] # Calculate FBC and corresponding Count based on grp_cols
    grp_cols = add_item(grp_cols, list_item)
    if 'DR'.upper() in test_for.upper():
        grp_cols.extend(['First_Read'])

    if 'B47R'.upper() in device or 'Micron'.upper() in device:
        df_Cell = df_init[df_init['Cell'] == mode].copy()
    df_block = df_Cell[['FBC'] + grp_cols].value_counts().reset_index(name='Count').sort_values('FBC', ascending=0) # for value_counts, it just output the grp_cols columns and remove other columns
    df_block['BER'] = df_block['FBC']/(codeword*8)
    return df_block
    
# ### *************************** Start: Get SFR and Merge RR based on block *************************** #
#     df_merge = pd.DataFrame()
#     FBC_col_list = ['FBC'] + grp_cols + ['BER']
#     grouped = df_block.groupby(grp_cols)
#     for name, group in grouped:
#         group_sum = group.groupby(FBC_col_list, as_index=False).sum().sort_values(by=['FBC'], ascending=0)
#         group_sum['cum_Count'] = group_sum['Count'].cumsum()
#         group_sum['SFR'] = group_sum['cum_Count']/group_sum['cum_Count'].max()
#         df_merge = pd.concat([df_merge, group_sum], ignore_index=True)
#     df_merge.drop(['cum_Count'], axis=1, inplace=True)   

def txt2csv_file(folder_path, store_dir, suffix_name, pids_list):
    try:
        df_init_csv, df_blk_csv = pd.DataFrame(), pd.DataFrame()
        df_init_list, df_blk_list, txt_file_list = [], [], []
        
        if 1:
            txt_file_list = [os.path.join(folder_path, file) for file in os.listdir(folder_path) if os.path.join(folder_path, file).endswith('.txt')]
        else:
            txt_file = re.compile(r"^(fully|RR_[0-4])")
            for file in os.listdir(folder_path):
                    if re.search(txt_file, file):
                        txt_file_list.append(os.path.join(folder_path, file))

        if txt_file_list:
            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                with tqdm(total=len(txt_file_list), desc=Fore.GREEN + '     [TXT2CSV Transition]', colour='GREEN', ncols=120, position=0) as tbar:
                    pfunc = partial(txt2csv_merge, tbar, test_for, df_init_list, df_blk_list, pids_list)
                    threadpool = ThreadPool(4)
                    threadpool.map(pfunc, txt_file_list)
                    threadpool.close()
                    threadpool.join()
            else:
                pfunc = partial(txt2csv_merge, 0, test_for, df_init_list, df_blk_list, pids_list)
                threadpool = ThreadPool(4)
                threadpool.map(pfunc, txt_file_list)
                threadpool.close()
                threadpool.join()

            df_init_csv = pd.concat(df_init_list, ignore_index=True)             
            df_init_list.clear()
            if 0:
                df_blk_csv = pd.concat(df_blk_list, ignore_index=True) 
                df_blk_list.clear()

                df_init_csv = reduce_mem_usage(df_init_csv)
                df_blk_csv = reduce_mem_usage(df_blk_csv)
                
                # # df_init_csv.info(memory_usage = 'deep')
                # tqdm.write('    [INFO] >>> PID-'  + str(os.getpid()) + "    'df_init_list' memory occupied: {:.2f} KB".format(sys.getsizeof(df_init_list)/(1024)))
                # tqdm.write('    [INFO] >>> PID-'  + str(os.getpid()) + "    'df_init_csv' memory occupied: {:.2f} MB".format((df_init_csv.memory_usage(deep=True).sum())/(1024**2)))
                # tqdm.write('    [INFO] >>> PID-'  + str(os.getpid()) + "    *******************************************************")
                # tqdm.write('    [INFO] >>> PID-'  + str(os.getpid()) + "    'df_blk_list' memory occupied: {:.2f} KB".format(sys.getsizeof(df_blk_list)/(1024)))
                # tqdm.write('    [INFO] >>> PID-'  + str(os.getpid()) + "    'df_blk_csv' memory occupied: {:.2f} MB".format((df_blk_csv.memory_usage(deep=True).sum())/(1024**2)))
            
            def write_csv(store_dir):
                df_init_csv.to_csv(store_dir + r'/Initialization_data_' + suffix_name + '.csv', header= True, index=False, mode='w+') # a+ to w+
                # df_blk_csv.to_csv(store_dir + r'/Data_by_blk_' + suffix_name + '.csv', header= True, index=False, mode='w+')
            threading.Thread(target=write_csv, kwargs={'store_dir': store_dir}).start()

            return df_init_csv
            # return df_init_csv, df_blk_csv
    
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def block_level_process(store_dir, df_blk_list, suffix_name, pids_list):
    try:
        if pids_list[-1] == os.getpid():
            print(Fore.BLUE + '\n\n[' + str(datetime.now()) + '] -> Page_level RR Optimize now ...')
        
        df_optimum_optimum_by_blk = pd.DataFrame()
        
        df_optimum_optimum_by_blk = Block_Level_Optimum_RR(store_dir, df_blk_list, suffix_name, pids_list)

        if pids_list[-1] == os.getpid():
            print(Fore.GREEN + '\n        [INFO] >>> Block_level Processing Procedure Start...')
        
        Processing_Data(store_dir, df_optimum_optimum_by_blk, 'block', suffix_name, pids_list)

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def page_level_process(store_dir, df_init, generate_optimum_file_flag, suffix_name, pids_list): # , ratio
    try:
        df_optimum_by_page = pd.DataFrame()
        if generate_optimum_file_flag:
            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                print(Fore.BLUE + '   [' + str(datetime.now()) + '] -> Page_level RR Optimize now ...')
            df_optimum_by_page = Page_Level_Optimum_RR(store_dir, df_init, suffix_name, pids_list) # , ratio
        
        if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
            print(Fore.BLUE + '   [' + str(datetime.now()) + '] -> Page_level BER/SFR Run now ...')
            print(Fore.GREEN + '        [INFO] >>> Page_level Processing Procedure Start...')

        Processing_Data(store_dir, df_optimum_by_page, 'page', suffix_name, pids_list)    #hang
        
        return df_optimum_by_page

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())


# ******************************* Start: Extract Best RR based on block ****************************** #
def Block_Level_Optimum_RR(store_dir, df_blk_list, suffix_name, pids_list):
    try:
        if pids_list[-1] == str(os.getpid()):
            print('    >>> Waiting to Open Blk-File', end=' ', flush=True)

        if df_blk_list.empty == False:
            # print('  -  df_blk_list Empty? ', df_blk_list.empty)
            df = df_blk_list
        else:
            for blk_file in os.listdir(store_dir):
                if "data_by_blk" in blk_file.lower():
                    # print(" - File Exist?: ", os.path.exists(os.path.join(store_dir, blk_file)))
                    df = pd.read_csv(os.path.join(store_dir, blk_file), low_memory=False)

        stress_name = [col_name for col_name in df.columns.values.tolist() if ndinfo.test_stress in col_name][0]
        grp_cols = ['Cycle','Block','Level','Type','Read',stress_name] + [die_locator]
        grp_cols = add_item(grp_cols, list_item)

        df_default = df[df['Read']=='Default Read'].copy()
        df_2ndRead = pd.DataFrame()
        if 'DR'.upper() in test_for.upper():
            df_2ndRead = df[(df['Condition'].str.contains('RR0')) & (df['Read']==retry_mode)].copy()
            df_2ndRead.loc[:, 'First_Read'] ='2nd Read'
            grp_cols.extend(['First_Read'])

        df_retry = df.loc[df['Read']==retry_mode]

        df_RR = pd.DataFrame()
        optitum_list = []
        if df_retry.empty == False:
            grouped = df_retry.groupby(grp_cols)
            with tqdm(total=len(grouped), desc=Fore.GREEN + '    [ PID-' + str(os.getpid()) + ' -> Blk-Optimum RR ]' , colour='GREEN', ncols=120, position=0) as tbar:
                for name, group in grouped:
                    select_group = None
                    max_fbc_limit = codeword * 8
                    avg_fbc_limit = codeword * 8
                    for rr_name, group_fbc in group.groupby('Condition'):
                        if group_fbc['FBC'].max() <= max_fbc_limit:
                            if group_fbc['FBC'].max() == max_fbc_limit:
                                if group_fbc['FBC'].mean() < avg_fbc_limit:
                                    best_entry = rr_name
                                    avg_fbc_limit = group_fbc['FBC'].mean()
                                    max_fbc_limit = group_fbc['FBC'].max()
                                    select_group = group_fbc
                            else:
                                avg_fbc_limit = group_fbc['FBC'].mean()
                                max_fbc_limit = group_fbc['FBC'].max()
                                best_entry = rr_name
                                select_group = group_fbc
                    optitum_list.append(select_group)
                    tbar.update()

            df_optitum = pd.concat(optitum_list, ignore_index=True)
        df_RR_outcome = pd.concat([df_default, df_2ndRead, df_optitum], ignore_index=True)

        if 'DR'.upper() in test_for.upper():
            col_order = ['FBC','Cycle','Block','Level'] + [die_locator] + ['Condition','Type','Read','First_Read',stress_name,'Count','BER']
            col_order = add_item(col_order, list_item)
            df_RR_outcome = df_RR_outcome.loc[:, col_order]
        else:
            col_order = ['FBC','Cycle','Block','Level'] + [die_locator] + ['Condition','Type','Read',stress_name,'Count','BER']
            col_order = add_item(col_order, list_item)
            df_RR_outcome = df_RR_outcome.loc[:, col_order]

        def write_csv(store_dir):
            df_RR_outcome.to_csv(store_dir + r'/Optimum_Read_by_blk_' + suffix_name + '.csv', index=False, mode = 'w+') 

        threading.Thread(target=write_csv, kwargs={'store_dir': store_dir}).start()

        return df_RR_outcome
    
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

# ******************************* End: Extract Best RR based on block ******************************** #
def page_level_file(df):
    try:
        df_page = df.copy()
        stress_name = [col_name for col_name in df.columns.values.tolist() if ndinfo.test_stress in col_name][0]
        grp_cols = ['Cycle', 'Block', 'Level', 'Layer', 'WordLine', 'Condition', 'Type', 'Read', stress_name, 'TestTemp'] + [die_locator]   # Level is the smallest dimension
        grp_cols = add_item(grp_cols, list_item)
        if 'DR'.upper() in test_for.upper():
            grp_cols.extend(['First_Read'])
        # hang, add  dropna=False in value_counts() because the First_Read is all None.
        # 'None' values are usually interpreted as NaN (null values) during operations like sorting, counting, or mathematical operations. 
        # This is because 'None' is the Python representation of null, while NaN is the pandas representation.
        # NaN values are ignored by default.
        # df_page = df[['FBC'] + grp_cols].value_counts(dropna=False).reset_index(name='Count').sort_values('FBC')  # for value_counts, it just output the grp_cols columns and remove other columns
        df_page = df[['FBC'] + grp_cols].value_counts().reset_index(name='Count').sort_values('FBC')    #hang, Linux 不支持 value_counts(dropna), pandas版本过低
        df_page['BER'] = df_page['FBC']/(codeword*8)

        del df #hang, free memory
        return df_page

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
    
# *************************** Start: Get SFR and Merge data based on page *************************** #
def Page_Level_Optimum_RR(store_dir, df_init, suffix_name, pids_list): # , ratio
    try:
        if pids_list[-1] == str(os.getpid()):
            print(Fore.GREEN + '        [INFO] >>> Waiting to Process Init-File')
        start_time = time.time()
        
        if df_init.empty == False and (pids_list[-1] == str(os.getpid())):
            print(Fore.GREEN + '        [INFO] >>> df_init_list Empty? {}'.format(df_init.empty))

        if df_init.empty == True:
            for init_file in os.listdir(store_dir):
                if "initial" in init_file.lower():
                    if (pids_list[-1] == str(os.getpid())):
                        print(" -  File Exist?: ", os.path.exists(os.path.join(store_dir, init_file)), end=' ')
                    df_init = pd.read_csv(os.path.join(store_dir, init_file), low_memory=False)
        
        df = df_init.copy()
        del df_init #hang, free memory
        if '@'in df.loc[0, 'Condition']:   #为什么这里没问题，hit_rate.py line517就报错 “0 is not in range”，hang
            df.loc[:, 'Condition'] = df['Condition'].apply(lambda x: x.split(' @')[1].split('_')[1])

        if 'B47R'.upper() in device or 'Micron'.upper() in device:
            df = df[df['Cell'] == mode]
            df.drop(['Cell'], axis=1, inplace=True)

        #hang, decrease WL count to process
        # df['WordLine'] = df['WordLine'].astype(int)
        # df = df.loc[df['WordLine']<800]

        df_data_by_page = page_level_file(df) #统计一个page(16KB)里面相同FBC的个数
        df_optimum_by_page = optimize_rr(df_data_by_page, test_for, pids_list) # , ratio         #每个wordline每个Level找出FBC最小的RR case和 default read.
        df_optimum_by_page['BER'] = df_optimum_by_page['BER'].apply(lambda x: "{:0.{}e}".format(x, 2))
        # def page_optimum_save_fun(store_dir, suffix_name):
        #     df_optimum_by_page.to_csv(store_dir + r'/Optimum_Read_by_page_' + suffix_name + '.csv', index = False, mode = 'w+')
        # threading.Thread(target=page_optimum_save_fun, kwargs={'store_dir': store_dir, 'suffix_name': suffix_name}).start()

        df_optimum_by_page.to_csv(store_dir + r'/Optimum_Read_by_page_' + suffix_name + '.csv', index = False, mode = 'w+') #hang, ensure last folder is ok

        end_time = time.time()
        
        if pids_list[-1] == str(os.getpid()):
            print(Fore.GREEN + '        [INFO] >>> Elapsed for Processing Init-File: {0:0.2f} mins'.format((end_time - start_time)/60))
            # print(Fore.GREEN + '        [INFO] >>> Elapsed for Processing Init-File: {0:0.2f} mins'.format((end_time - start_time)/60))
        
        return df_optimum_by_page
    
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
# *************************** End: Get SFR and Merge data based on page ***************************** #

def convert2avg_max(df, group_list, store_dir, type_mode, data_type, suffix_name):
    list_a = []
    df['FBC_Sum'] = df['FBC'] * df['Count']
    for name, group in df.groupby(group_list, as_index=False):

        temporary = list(name)
        temporary.extend([group["FBC"].max(), "{:.2f}".format(group["FBC_Sum"].sum()/group["Count"].sum())]) # Max_FBC, Avg_FBC

        if 'BER' in data_type:
            temporary.extend(["{:0.{}e}".format((group["FBC"].max())/(codeword*8), 2), "{:0.{}e}".format((group["FBC_Sum"].sum()/group["Count"].sum())/(codeword*8), 2)]) # Max_BER, Avg_FBC
        # if 'byWL' in data_type:
        #     fbc_cnt_dict = dict()
        #     for fbc in group['FBC'].unique():
        #         fbc_cnt_dict[fbc] = group.loc[group['FBC']==fbc, 'Count'].tolist()[0]
        #     temporary.append(fbc_cnt_dict)

        list_a.append(temporary) 
    
    df_BER = pd.DataFrame(list_a)
    if 'BER' in data_type:
        df_BER.columns = group_list + ['Max_FBC', 'Avg_FBC', 'Max_BER', 'Avg_BER']
    else:
        df_BER.columns = group_list + ['Max_FBC', 'Avg_FBC']

    df_BER = df_BER.sort_values(by=['Max_FBC'], ascending = 0).reset_index(drop=True)
    
    path_ber = os.path.join(store_dir, type_mode + '_level_' + data_type + '_' + suffix_name + '.csv')
    df_BER.to_csv(path_ber, index=False, mode = 'w+')
    
#*********************************** Start: Page-level Optimum CSV ********************************** #
def optimize_rr(df, test_for, pids_list):
    try:
        stress_name = [c for c in df.columns if ndinfo.test_stress in c][0]
        grp_cols = ['Block', 'Level', 'WordLine', 'Type', stress_name,'TestTemp'] + [die_locator]
        grp_cols = del_item(df, grp_cols, stress_name)
        grp_cols = add_item(grp_cols, list_item)
        
        df_default = df.loc[df['Read']=='Default Read'] # .copy()       
        df_2ndRead = pd.DataFrame()
        if 'DR'.upper() in test_for.upper():
            df_2ndRead = df[(df['Condition'].str.contains('RR0')) & (df['Read']=='Read Retry')] # .copy() #hang, change from retry_mode to 'Read Retry'
            df_2ndRead.loc[:, 'First_Read'] ='2nd Read'

        # df_retry = df.loc[df['Read']==retry_mode] #col "Read" 里面只有Default Read 和 Read Retry, hang
        if optimum_read_by_page_specific_RR ==1:    #hang, 根据自己的需要修改
            df_retry = df.loc[(df['Read'] == 'Read Retry') & (df['Condition'].apply(lambda x: int(x.split('RR')[1]) < 116 if 'RR' in x else False))]
        else:
            df_retry = df.loc[df['Read']=='Read Retry'] #hang
        df_opt = pd.DataFrame()
        if not df_retry.empty:
            if 'DR'.upper() in test_for.upper():
                # df_retry["First_Read"].replace(['2nd Read'], ["None"], inplace = True)
                df_retry["First_Read"].replace(['2nd Read'], ['Not1st2nd'], inplace = True)  #hang
            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                with tqdm(total=len(df_retry.groupby(grp_cols).groups.keys()), desc=Fore.GREEN + '     [Extract Page-Optimum Best RR]', colour='GREEN', ncols=120, position=0) as tbar:  
                    df_grp1 = df_retry.groupby(grp_cols) # grp_cols is to filter for cycle, block, levle...
                    if df_grp1.ngroups > 0:
                        grp1_conds = df_grp1.groups.keys() # 'Condition' contains all RRs, find out best RR under grp1_conds.groupby('Condition')
                        df_grp2_dict = {g: df_grp1.get_group(g).groupby('Condition') for g in grp1_conds if len(df_grp1.get_group(g)) > 0}
                    else:
                        print("df_grp1 is empty")
                    
                    min_fbc_dict = {}
                    for g1, df_list in df_grp2_dict.items():
                        max_fbcs = {g1: df_list['FBC'].max()} #找出每个RR的最大FBC，因为有4个4K page
                        max_f_np = np.array(list(max_fbcs.values()))
                        min_idx = np.where(max_f_np==np.min(max_f_np))[1][0]
                        df = max_fbcs[g1].to_frame().reset_index(drop=False)
                        min_fbc_dict[g1] =  df.loc[min_idx, 'Condition']

                        tbar.update()
            else:
                df_grp1 = df_retry.groupby(grp_cols) #按照grp_col分组了
                if df_grp1.ngroups > 0:
                    grp1_conds = df_grp1.groups.keys() #groups返回一个类似字典的对象，其中键是组的名称。然后使用keys（）方法将这些键提取到一个名为grp1_conds的列表中。
                    df_grp2_dict = {g: df_grp1.get_group(g).groupby('Condition') for g in grp1_conds if len(df_grp1.get_group(g)) > 0}  #再将每组按照Condition（包含所有的RRs）分组
                else:
                    print("df_grp1 is empty")
                
                min_fbc_dict = {}
                for g1, df_list in df_grp2_dict.items():
                    max_fbcs = {g1: df_list['FBC'].max()}
                    max_f_np = np.array(list(max_fbcs.values()))
                    min_idx = np.where(max_f_np==np.min(max_f_np))[1][0]
                    df = max_fbcs[g1].to_frame().reset_index(drop=False)
                    min_fbc_dict[g1] =  df.loc[min_idx, 'Condition']

            df_opt = pd.concat([df_grp2_dict[g1].get_group(idx) for g1, idx in min_fbc_dict.items() if idx in df_grp2_dict[g1].groups.keys()])
            
        final_df = pd.concat([df_default, df_opt, df_2ndRead], ignore_index=True)

        if 'DR'.upper() in test_for.upper():
            reorder_cols = ['FBC','Cycle','Block','Level','Layer','WordLine'] + [die_locator] + ['Condition','Type','Read','First_Read',stress_name,'TestTemp','Count','BER']
            reorder_cols = add_item(reorder_cols, list_item)
            final_df = final_df.loc[:, reorder_cols]
        else:
            reorder_cols = ['FBC','Cycle','Block','Level','Layer','WordLine'] + [die_locator] + ['Condition','Type','Read',stress_name,'TestTemp','Count','BER']
            reorder_cols = add_item(reorder_cols, list_item)
            final_df = final_df.loc[:, reorder_cols]

        return final_df
        
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

#Hang, add this section for process best RR entries 
#*********************************** Start: Page-level Optimum CSV ********************************** #
def filter_rr(store_dir, suffix_name, folder):
    try:
        with tqdm(range(1), desc=Fore.GREEN + '     [Processiong csv Files for Folder {}'.format(folder) + ']: ', colour='GREEN', ncols=120, position=0) as tbar:
            file = r'Initialization_data' + '_'  + suffix_name + '.csv'
            file_path = os.path.join(store_dir, file)
            df = pd.read_csv(file_path, low_memory=False)
            # rrtabledf = pd.read_csv(rrtable_path, low_memory=False)
            df['Temp'] = folder.split('_')[3]
            # df['Type'] = folder.split('_')[2]
            stress_name = [c for c in df.columns if ndinfo.test_stress in c][0]
            df_retry = df.loc[df['Read']=='Read Retry']
            df_filter_rr = pd.DataFrame()
            df_rr = pd.DataFrame()
            if not df_retry.empty:
                for rr_num in RR_output_list:
                    rr_entry = 'RR'+ str(rr_num)
                    df_rr = df_retry[(df_retry['Condition'].str.contains(rr_entry)) & (df_retry['Read']=='Read Retry')]
                    if not df_rr.empty:
                        df_filter_rr = pd.concat([df_filter_rr, df_rr], ignore_index=True)                
            reorder_cols = ['FBC','Cycle','Level','WordLine', 'Condition','Type',stress_name,'Temp'] #] + [die_locator] + ['Block','Read',
            df_filter_rr = df_filter_rr.loc[:, reorder_cols]
            # df_filter_rr = df_filter_rr.set_index('Condition').join(rrtabledf.set_index('Condition')).reset_index() #hang,将两个表按照Condition合并
            # print(df_filter_rr.head(5))
            # df_filter_rr.to_csv(store_dir + r'\Filter_RR_Read_by_page_' + suffix_name + '.csv', index = False, mode = 'w+')

            if split_WL_zone_flag==1:
                def determine_edgewl_rrgroup(x):
                    if 1580<= x <=1599:
                        return 'Group0'
                    elif 0<= x <=419:
                        return 'Group1'
                    elif 420<= x <=1099:
                        return 'Group2'
                    elif 1100<= x <=1369:
                        return 'Group3'
                    elif 1370<= x <=1499:
                        return 'Group4'
                    else:
                        return 'Group7'
                def determine_inner_rrgroup(x):
                    if 1500<= x <=1589:
                        return 'Group0'
                    elif 0<= x <=829:
                        return 'Group5'
                    else:
                        return 'Group6'
                        
            if (('Partial+DR' == test_for) | ('SPRD' == test_for) | ('Partial+SPRD' == test_for)):
                if 'Partial+DR' == test_for:#hang, add to split inner/edge wordline
                    for partial in df_filter_rr['Type'].unique():
                        if '100%' not in partial:
                            maxWL = df_filter_rr[df_filter_rr['Type']==partial].loc[:,'WordLine'].max()
                            df_filter_rr['WLType'] = df_filter_rr[df_filter_rr['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'InnerWL' if x < (maxWL-wl_per_layer+1) else 'EdgeWL')
                        else:
                            df_filter_rr.loc[df_filter_rr['Type']==partial,'WLType'] = 'Close'

                        if split_WL_zone_flag==1:
                            WLtype_list = df_filter_rr['WLType'].unique()
                            if (('EdgeWL' in WLtype_list) | ('InnerWL' in WLtype_list)):  #一个folder里只有一个type
                                # open block edge wl
                                df_filter_rr.loc[df_filter_rr['WLType'] == 'EdgeWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                                # open bllock inner wl
                                df_filter_rr.loc[df_filter_rr['WLType'] == 'InnerWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'InnerWL', 'WordLine'].apply(determine_inner_rrgroup)                    
                            else:
                                # close block
                                df_filter_rr.loc[:,'RRGroup'] = 'Group0'
                            print('WLtype:', df_filter_rr['WLType'].unique())
                            print('RRGroup:', df_filter_rr['RRGroup'].unique())                                    
                elif 'SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
                    SPRD_Aggressor_Layer = SPRD_Aggressor_WL//wl_per_layer   
                    df_filter_rr['WLType'] = df_filter_rr.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1)|(x//wl_per_layer==SPRD_Aggressor_Layer+1) else 'Non-VictimWL')
                    # df_filter_rr['WLType'] = df_filter_rr.loc[:,'WordLine'].apply(lambda x: 'VictimWL' if (x//wl_per_layer==SPRD_Aggressor_Layer-1) else 'Non-VictimWL')                
                elif 'Partial+SPRD' == test_for:  #hang, add to split SPRD aggressor/victim wordline
                    for partial in df_filter_rr['Type'].unique(): 
                        maxWL = df_filter_rr[df_filter_rr['Type']==partial].loc[:,'WordLine'].max()
                        df_filter_rr['WLType'] = df_filter_rr[df_filter_rr['Type']==partial].loc[:,'WordLine'].apply(lambda x: 'Victim_EdgeWL' if x>=(maxWL//wl_per_layer)*wl_per_layer else ('Aggressor_EdgeWL' if (x>(maxWL-wl_per_layer) and x<(maxWL//wl_per_layer)*wl_per_layer) else ('Victim_InnerWL' if ((x//wl_per_layer)==(maxWL//wl_per_layer -2)) else ('InnerWL' if (x//wl_per_layer<(maxWL//wl_per_layer -2)) else 'Aggressor_InnerWL'))))                    
                    if split_WL_zone_flag==1:
                        # open block edge wl
                        df_filter_rr.loc[df_filter_rr['WLType'] == 'Victim_EdgeWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'Victim_EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                        df_filter_rr.loc[df_filter_rr['WLType'] == 'Aggressor_EdgeWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'Aggressor_EdgeWL','WordLine'].apply(determine_edgewl_rrgroup)
                        # open bllock inner wl
                        df_filter_rr.loc[df_filter_rr['WLType'] == 'Victim_InnerWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'Victim_InnerWL', 'WordLine'].apply(determine_inner_rrgroup)
                        df_filter_rr.loc[df_filter_rr['WLType'] == 'InnerWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'InnerWL', 'WordLine'].apply(determine_inner_rrgroup)
                        df_filter_rr.loc[df_filter_rr['WLType'] == 'Aggressor_InnerWL','RRGroup'] = df_filter_rr.loc[df_filter_rr['WLType'] == 'Aggressor_InnerWL', 'WordLine'].apply(determine_inner_rrgroup)                    
                        print('WLtype:', df_filter_rr['WLType'].unique())
                        print('RRGroup:', df_filter_rr['RRGroup'].unique())

                if split_WL_zone_flag==1:     
                    group_list = ['Cycle','Type','Level','Condition','WLType','RRGroup','Temp',stress_name]
                    finaldf = df_filter_rr.loc[df_filter_rr.groupby(group_list)['FBC'].idxmax()] #hang, get the max FBC by group
                    if os.path.exists(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType_RRGroup.csv'): #hang, 
                        finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType_RRGroup.csv', index = False, header=0,mode = 'a+')
                    else:
                        finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType_RRGroup.csv', index = False, mode = 'w+')
                else:
                    group_list = ['Cycle','Type','Level','Condition','WLType','Temp',stress_name]
                    finaldf = df_filter_rr.loc[df_filter_rr.groupby(group_list)['FBC'].idxmax()] #hang, get the max FBC by group
                    if os.path.exists(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType.csv'): #hang, 
                        finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType.csv', index = False, header=0,mode = 'a+')
                    else:
                        finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR_WLType.csv', index = False, mode = 'w+')
            else:
                group_list = ['Cycle','Type','Level','Condition','Temp',stress_name]
                finaldf = df_filter_rr.loc[df_filter_rr.groupby(group_list)['FBC'].idxmax()] #hang, get the max FBC by group
                if os.path.exists(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR.csv'): #hang, 
                    finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR.csv', index = False, header=0, mode = 'a+')
                else:
                    finaldf.to_csv(ndinfo.path + r'/MaxFBC_by_Cycle_Type_Level_RR.csv', index = False, mode = 'w+')                        
        tbar.update()
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

# *************************** Start: Get SFR specified value Data *************************** #
def func(x, A, B):
    return A * x + B

def get_FBC_SFR(group_sum, SFR_spec):
    try:
        group_sum_filter = group_sum[(group_sum['SFR']>=1e-2) & (group_sum['SFR']<1e-0)].copy()
        f1 = lambda x: math.log10(x)
        group_sum_filter['SFR_log'] = group_sum_filter.loc[:,'SFR'].apply(f1)
        y0 = group_sum_filter['FBC']
        x0 = group_sum_filter['SFR_log']
        
        A1, B1 = curve_fit(func, x0, y0)[0]
        x1 = math.log10(SFR_spec)
        y1 = A1 * x1 + B1
        return y1

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
# *************************** End: Get SFR specified value Data *************************** #


# *************************** Below: Obtain SFR.csv File *************************** #
def Processing_Data(store_dir, optimum_data, type_mode, suffix_name, pids_list):
    try:
        start_time = time.time()

        df_SFR = pd.DataFrame()
        df_SFR_trend = pd.DataFrame()

        # if optimum_data.empty == False: #hang, 报错 AttributeError: 'NoneType' object has no attribute 'empty'
        if optimum_data is not None and not optimum_data.empty:
            df_optimal = optimum_data.copy()
        else:
            if 'block' in type_mode:
                optimum_csv = store_dir + r'/Optimum_Read_by_blk_' + suffix_name + '.csv'
            elif 'page' in type_mode:
                optimum_csv = store_dir + r'/Optimum_Read_by_page_' + suffix_name + '.csv'

            df_optimal = pd.read_csv(optimum_csv, low_memory=False) 

        stress_name = [col_name for col_name in df_optimal.columns.values.tolist() if ndinfo.test_stress in col_name][0]       
        group_list_1 = ['Cycle', 'Level', 'Type', 'Read', stress_name,'TestTemp']
        group_list_1 = add_item(group_list_1, list_item)
        group_list_2 = ['Cycle', 'Type','Read', stress_name,'TestTemp']
        group_list_2 = add_item(group_list_2, list_item)
        
        group_list_byWL = ['Cycle', 'Block', 'Level', 'WordLine', 'Read', 'Type', die_locator, stress_name,'TestTemp']
        group_list_byWL = del_item(df_optimal, group_list_byWL)
        group_list_byWL = add_item(group_list_byWL, list_item)
        
        if list_item and 'Plane' in list_item:
            group_list_1.remove('Plane')
            group_list_2.remove('Plane')
        
        if 'DR'.upper() in test_for.upper():
            group_list_1.extend(['First_Read'])
            group_list_2.extend(['First_Read'])
            group_list_byWL.extend(['First_Read'])

        df_SFR_1, df_SFR_trend_1 = SFR_Cal(df_optimal, group_list_1)
        df_SFR_2, df_SFR_trend_2 = SFR_Cal(df_optimal, group_list_2)
        df_SFR_2['Level'] = 'All'
        df_SFR_2 = df_SFR_2[df_SFR_1.columns]
        df_SFR_trend_2['Level'] = 'All'
        df_SFR_trend_2 = df_SFR_trend_2[df_SFR_trend_1.columns]

        df_SFR = pd.concat([df_SFR_1, df_SFR_2], ignore_index=True)
        df_SFR['SFR'] = df_SFR['SFR'].apply(lambda x: "{:0.{}e}".format(x, 2))
        df_SFR['BER'] = df_SFR['BER'].apply(lambda x: "{:0.{}e}".format(x, 2))
        path_SFR = os.path.join(store_dir, type_mode + '_level_SFR_' + suffix_name + '.csv')    
        df_SFR.to_csv(path_SFR, index=False, mode = 'w+')

        df_SFR_trend = pd.concat([df_SFR_trend_1, df_SFR_trend_2], ignore_index=True)
        df_SFR_trend['SFR@'+str(SFR_spec)] = df_SFR_trend['SFR@'+str(SFR_spec)].apply(lambda x: "{:.2f}".format(x))
        path_SFR_trend = os.path.join(store_dir, type_mode + '_level_SFR_trend_' + suffix_name + '.csv')   
        df_SFR_trend.to_csv(path_SFR_trend, index=False, mode = 'w+')

        ### *********************************** Below: Generate BER.csv File *********************************** ###
        df_SFR_for_BER = df_SFR[['FBC'] + group_list_1 + ['BER'] + ['Count']]
        
        if pids_list[-1] == str(os.getpid()):
            print(Fore.GREEN + '        [INFO] >>> BER/SFR Procedure Start...')
        convert2avg_max(df_SFR_for_BER, group_list_1, store_dir, type_mode, 'BER', suffix_name)

        if pids_list[-1] == str(os.getpid()):
            print(Fore.GREEN + '        [INFO] >>> ByWL Processing Procedure Start...')
        convert2avg_max(df_optimal, group_list_byWL, store_dir, type_mode, 'byWL', suffix_name)

        end_time = time.time()

        if pids_list[-1] == str(os.getpid()):
            print(Fore.GREEN + '        [INFO] >>> ' + type_mode.capitalize() + '-level BER/SFR Finished: {0:.2f} mins'.format((end_time - start_time)/60))

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())


def SFR_Cal(df_optimal_csv, grp_cols):   
    try: 
        FBC_col_list = ['FBC'] + grp_cols + ['BER']
        FBC_SFR_col_list = grp_cols + ['SFR@'+str(SFR_spec)]
        df_optimal_csv = df_optimal_csv[['FBC'] + grp_cols + ['Count']].sort_values(by=['FBC'], ascending=0)

        ### *********************************** Calculate BER value ************************************ ###
        df_optimal_csv.loc[:, 'BER'] = df_optimal_csv.loc[:, 'FBC']/(codeword*8)
        df_SFR = pd.DataFrame()
        df_SFR_trend_all = pd.DataFrame()

        ### ************************************ Calculate SFR value ************************************ ###
        grouped = df_optimal_csv.groupby(grp_cols)
        for name, group in grouped:
            group_sum = group.groupby(FBC_col_list, as_index=False).sum().sort_values(by=['FBC'], ascending=0)
            group_sum['cum_Count'] = group_sum['Count'].cumsum()
            group_sum['SFR'] = group_sum['cum_Count']/group_sum['cum_Count'].max()
            df_SFR = pd.concat([df_SFR, group_sum], ignore_index=True)
            if mode in ['TLC', 'QLC']:   #hang, change from if mode == 'TLC': to if mode in ['TLC', 'QLC']:
                FBC_SFR = get_FBC_SFR(group_sum, SFR_spec)  
                FBC_SFR_trend = pd.DataFrame([list(name)+[FBC_SFR]], columns=FBC_SFR_col_list)
                df_SFR_trend_all = pd.concat([df_SFR_trend_all, FBC_SFR_trend], ignore_index=True)

        df_SFR.drop(['cum_Count'], axis=1, inplace=True)

        return df_SFR, df_SFR_trend_all

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
