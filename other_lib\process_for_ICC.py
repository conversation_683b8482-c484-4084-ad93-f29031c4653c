import csv
import re
import os
import math
import time
import shutil
import xlrd
import xlwt
import openpyxl
import xlsxwriter
import xlutils.copy
import numpy as np
import pandas as pd
from scipy import optimize
from scipy.stats import norm
import pandas.io.formats.excel
from openpyxl.styles import Alignment
pandas.io.formats.excel.header_style = None
from openpyxl.utils import get_column_letter

print(openpyxl.__version__)
def f1(x):
    if x % 3 == 0:
        return 'LSB'
    elif x % 3 == 1:
        return 'CSB'
    elif x % 3 == 2:
        return 'MSB'

def f2(x): # //Page Marking
    if x < 4:
        return 'SLC'
    if x <= 1063 and x >= 1048:
        return 'MLC'
    if x >= 2108:
        return 'SLC'  
    
    elif x % 3 == 0:
        return 'MSB'if x < 1064 else 'CSB'
    elif x % 3 == 1:
        return 'LSB'if x < 1064 else 'MSB'
    elif x % 3 == 2:
        return 'CSB'if x < 1064 else 'LSB'


def f3(x): # //WL Marking
    if x <4:
        return x
    elif x >= 1048 and x <= 1063:
        return (x-1048)//2+352
    elif x >= 2108:
        return x-1400
    elif x > 1063 and x < 2108:
        return (x+16)//3
    else:
        return (x+8)//3


def worksheet_data(writer, table, item, label, name, startrow, startcol=0):
    # table.columns =[s1 for (s1, s2) in table.columns.tolist()] #hang
    table.reset_index(inplace=True)
    table.to_excel(writer, sheet_name=name, startrow=startrow, startcol=startcol)
    workbook = writer.book
    worksheets = writer.sheets
    worksheet = worksheets[name]
    if 'tR' in item:
        format1 = workbook.add_format({'num_format': '0.00', 'align': 'center', 'valign': 'vcenter'})
    elif 'tPROG' in item or 'tERS' in item:
        format1 = workbook.add_format({'num_format': '0', 'align': 'center', 'valign': 'vcenter'})
    else:
        format1 = workbook.add_format({'num_format': '0.0', 'align': 'center', 'valign': 'vcenter'})
    format2 = workbook.add_format({'align': 'center', 'valign': 'vcenter'})
    
    if 'ICC' in label:
        worksheet.write(startrow, startcol, item +'_'+ label + ':[mW]')
    return worksheet, format1, format2


def get_pivot_table(df_source, item, label, name, writer):
    # get_pivot_table(df_source, 'IAvg', 'ICC4r ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
    print('item: {0} - label: {1} - name: {2}'.format(item, label, name))
    df_data = df_source[(df_source['Measurement'].str.contains(item[-3:])) & (df_source['Label'] == label)].copy()
    df_data['Value'] = df_data['Value'].astype(float)
    df_data['Blk_Level'] = df_data['Block'].astype(str) + df_data['Level']

    if 'Cycling' in label:
        df_data = df_data[(df_data['Value']>10)]

    if ('ICC' in label) and ('ICCQ' not in label):
        if 'Datatransfer' in name:
            if 'MP-PRG' in name or 'PRG-MP' in name:
                df_filter = df_data[df_data['Blk_Level'] == df_data['Block'].astype(str) + 'MSB'].copy()
            elif ('PRG' in name) and (('MP-PRG' not in name) or ('PRG-MP' not in name)):
                df_filter = df_data[df_data['Level'] == 'MSB'].copy()
            else:
                df_filter = df_data.copy()
        else:
            if 'ERS' in name or 'READ_' in name and (('MP-READ' not in name) or ('READ-MP' not in name)):
                df_filter = df_data.copy()
            elif 'MP-READ' in name or 'READ-MP' in name:
                # print('Block: ', max(df_data['Block'].unique()))
                df_filter = df_data[df_data['Block'] == max(df_data['Block'].unique())].copy()
            elif 'MP-PRG' in name or 'PRG-MP' in name:
                df_filter = df_data[df_data['Blk_Level'] == df_data['Block'].astype(str) +'MSB'].copy()
            elif ('PRG' in name) and (('MP-PRG' not in name) or ('PRG-MP' not in name)):
                df_filter = df_data[df_data['Level'] == 'MSB'].copy()

        df_filter['Icc_Value'] = df_filter['Value'].astype(float)
        df_filter['Power_Value'] = df_filter['Icc_Value'] * Vcc
        if 'Avg' in item:
            if 'READ' in name and 'ALL' not in name:
                table = df_filter.pivot_table(index = ['Temperature','Level'], values = ['Power_Value'], aggfunc = {'Power_Value': ['mean']})
                table.columns = ['Power_Value_mean']
                worksheet, format1, format2 = worksheet_data(writer, table, item, label, name, startrow=0)
                worksheet.set_column("D:D", 10, cell_format=format1)

            else:
                table = df_filter.pivot_table(index = ['Temperature'], values = ['Power_Value'], aggfunc = {'Power_Value': ['mean']})
                table.columns = ['Power_Value_mean']
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=0)
                worksheet.set_column("C:C", 10, cell_format=format1)

        elif 'Max' in item:
            if 'READ' in name and 'ALL' not in name:
                table = df_filter.pivot_table(index = ['Temperature','Level'], values = ['Power_Value'], aggfunc = {'Power_Value': ['max']})
                table.columns = ['Power_Value_max']
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=11)
                worksheet.set_column("D:D", 10, cell_format=format1)
            else:
                table = df_filter.pivot_table(index = ['Temperature'], values = ['Power_Value'], aggfunc = {'Power_Value': ['max']}) 
                table.columns = ['Power_Value_max']
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=5)
                worksheet.set_column("C:C", 10, cell_format=format1)

        worksheet.set_column("A:B", 20, format2)

    elif 'ICCQ' in label:
        if 'ERS' in name or 'READ' in name:
            df_filter = df_data.copy()
        elif 'MP-PRG' in name or 'PRG-MP' in name:
            df_filter = df_data[df_data['Blk_Level'] == df_data['Block'].astype(str) + 'MSB'].copy()
        elif ('PRG' in name) and (('MP-PRG' not in name) or 'PRG-MP' not in name):
            df_filter = df_data[df_data['Level'] == 'MSB'].copy()

        df_filter['Icc_Value'] = df_filter['Value'].astype(float)
        df_filter['Power_Value'] = df_filter['Icc_Value'] * Vccq

        if 'Avg' in item:
            if ('READ' in name) and ('ALL' not in name):
                table = df_filter.pivot_table(index = ['Temperature','Level'], values = ['Power_Value'], aggfunc={'Power_Value': ['mean']})
                table.columns = ['Power_Value_mean']
                # Read by level
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=0, startcol=7)
                worksheet.set_column("K:K", 10, cell_format=format1)
                worksheet.set_column("J:J", 10, cell_format=format2)

            else:
                table = df_filter.pivot_table(index = ['Temperature'], values = ['Power_Value'], aggfunc={'Power_Value': ['mean']})
                table.columns = ['Power_Value_mean']
                # print('table: ', table)
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=0, startcol=7)
                # worksheet.set_column("H:I", 20,format2)
                worksheet.set_column("J:J", 10, cell_format=format1)


        elif 'Max' in item:
            if ('READ' in name) and ('ALL' not in name):
                table = df_filter.pivot_table(index = ['Temperature','Level'], values = ['Power_Value'], aggfunc={'Power_Value': ['max']})  #hang
                table.columns = ['Power_Value_max'] #hang
                #Read by level
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=11, startcol=7)
                worksheet.set_column("K:K", 10, cell_format=format1)
                worksheet.set_column("J:J", 10, cell_format=format2)
            else:
                table = df_filter.pivot_table(index = ['Temperature'], values = ['Power_Value'], aggfunc={'Power_Value': ['max']})  #hang
                table.columns = ['Power_Value_max'] #hang
                worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=5, startcol=7)
                # worksheet.set_column("H:I", 20, format2)
                worksheet.set_column("J:J", 10, cell_format=format1)

        worksheet.set_column("H:I", 22, cell_format=format2)
        
    else:
        if 'tR' in item:
            # table = df_data.pivot_table(index=['Temperature', 'Cycle'], values=['Value'], aggfunc=[np.min, np.avg, np.max]) 
            table = df_data.pivot_table(index=['Temperature', 'Cycle'], values=['Value'], aggfunc={'Value': ['min', 'mean', 'max']})    #hang
            table.columns = ['Value_min', 'Value_mean', 'Value_max']
            table_2 = df_data.pivot_table(index=['Temperature','Level', 'Cycle'], values=['Value'], aggfunc={'Value': ['min', 'mean', 'max']})  #hang
            table_2.columns = ['Value_min', 'Value_mean', 'Value_max']
        else:
            table = df_data.pivot_table(index=['Temperature', 'Cycle'], values=['Value'], aggfunc={'Value': ['min', 'mean', 'max']})
            table.columns = ['Value_min', 'Value_mean', 'Value_max']

        worksheet, format1, format2  = worksheet_data(writer, table, item, label, name, startrow=0, startcol=0)
        if 'tR' in item:
            worksheet, format1, format2  = worksheet_data(writer, table_2, item, label, name, startrow=6, startcol=0)

        worksheet.set_column("B:B", 16, cell_format=format2)
        worksheet.set_column("C:C", 10, cell_format=format2)
        worksheet.set_column('D:G', 10, cell_format=format1)
    

# *************************** Start: get_powerprofile *************************** #

def get_power(): 
    df = pd.read_csv(file_path, low_memory=False)
    df['Temperature'] = df['Temperature'].apply(lambda x: str(int(x))+'C')

    if 'Micron' in file_path:
        df['WL'] = df['Page'].apply(lambda x: f3(x))
        df['Level'] = df['Page'].apply(lambda x: f2(x))

    else:
        df['WL'] = df['Page'].apply(lambda x: (x)//3)
        df['Level'] = df['Page'].apply(lambda x: f1(x))

    df = df.dropna(axis=0, subset = ["SegmentName"])
    func_dict={'ERS-MP':'3','MP-ERS':'3','ERS':'3','PRG-MP':'2','MP-PRG':'2','PRG':'2','READ-MP':'1','MP-READ':'1','READ':'1'}

    df_icc= df[(df['SegmentName'].str.contains('ICC'))].copy() #& ~(df['SegmentName'].str.contains('2P'))
    df_icc=df_icc.dropna(subset=['Function'], axis=0)
    df_icc=df_icc[~(df_icc['Function']=='TURN_OFF')]
    fun_list = df_icc['Function'].unique()

    print('fun_list: ', fun_list)
    for func in fun_list:
        if 'READ'==func:
            writer = pd.ExcelWriter(os.path.join(des_path, 'SP-READ_Power.xlsx'), engine='xlsxwriter')
            workbook = writer.book
            worksheets = writer.sheets
            # writer = xlsxwriter.Workbook(os.path.join(des_path, 'SP-READ_Power.xlsx'))
        else:
            writer = pd.ExcelWriter(os.path.join(des_path, func+'_Power.xlsx'), engine='xlsxwriter')
            workbook = writer.book
            worksheets = writer.sheets
            # writer = xlsxwriter.Workbook(os.path.join(des_path, func+'_Power.xlsx'))

        if 'PRG' in func or 'READ' in func:
            df_micron = df_icc[df_icc['Level']!='SLC'].copy()
            df_micron_2 = df_micron[df_micron['Level']!='MLC'].copy()
            df_micron_3 = df_micron_2[df_micron_2['Function']==func].copy()
            df_source = df_micron_3.copy()

        else:
            df_micron_4 = df_icc[df_icc['Function']==func].copy()
            df_source = df_micron_4.copy()

        if 'MP' in func:
            plane_type = 'MP'
        else:
            plane_type = 'SP'
        
        if 'READ' in func:
            get_pivot_table(df_source, 'IAvg', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power_ALL', writer)
            get_pivot_table(df_source, 'IMax', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power_ALL', writer)
            get_pivot_table(df_source, 'IAvg', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power_ALL', writer)
            get_pivot_table(df_source, 'IMax', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power_ALL', writer)
            
            if 1:
                get_pivot_table(df_source, 'IAvg', 'ICC4r ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IMax', 'ICC4r ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IAvg', 'ICCQ4r ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IMax', 'ICCQ4r ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
            else:
                get_pivot_table(df_source, 'IAvg', 'ICC4 ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IMax', 'ICC4 ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IAvg', 'ICCQ4 ' + plane_type, func+'_Datatransfer_Power_ALL', writer)
                get_pivot_table(df_source, 'IMax', 'ICCQ4 ' + plane_type, func+'_Datatransfer_Power_ALL', writer)

            get_pivot_table(df_source, 'IAvg', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IAvg', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            
            if 1:
                get_pivot_table(df_source, 'IAvg', 'ICC4r ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICC4r ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IAvg', 'ICCQ4r ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICCQ4r ' + plane_type, func+'_Datatransfer_Power', writer)
            else:
                get_pivot_table(df_source, 'IAvg', 'ICC4 ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICC4 ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IAvg', 'ICCQ4 ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICCQ4 ' + plane_type, func+'_Datatransfer_Power', writer)

        elif 'PRG' in func:
            get_pivot_table(df_source, 'IAvg', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IAvg', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)

            if 1:
                get_pivot_table(df_source, 'IAvg', 'ICC4w ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICC4w ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IAvg', 'ICCQ4w ' + plane_type, func+'_Datatransfer_Power', writer)
                get_pivot_table(df_source, 'IMax', 'ICCQ4w ' + plane_type, func+'_Datatransfer_Power', writer)

        else:
            get_pivot_table(df_source, 'IAvg', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICC'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IAvg', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)
            get_pivot_table(df_source, 'IMax', 'ICCQ'+func_dict[func]+' ' + plane_type, func+'_Power', writer)

        writer.close()


# *************************** Start: get_performance *************************** #

def get_performance(plane_type):

    df = pd.read_csv(file_path, low_memory=False)
    df['Temperature'] = df['Temperature'].apply(lambda x: str(int(x))+'C')
    print('Temperature: ', df['Temperature'].unique())

    if 'Micron' in file_path:
        df['WL'] = df['Page'].apply(lambda x: f3(x))
        df['Level'] = df['Page'].apply(lambda x: f2(x))
        df['Cycle'] = df['Block'].apply(lambda x: blk2pec(x))

    else:
        df['WL'] = df['Page'].apply(lambda x: (x)//3)
        df['Level'] = df['Page'].apply(lambda x: f1(x))
        df['Cycle'] = df['Block'].apply(lambda x: blk2pec(x))

    df = df.dropna(axis=0, subset = ["SegmentName"])   # 丢弃‘Age’和‘Sex’这两列中有缺失值的行    
    df_cycle= df[(df['SegmentName'].str.contains('PD')) | (df['SegmentName'].str.contains('MultiPlaneCycling'))].copy()
    print(df_cycle['SegmentName'].unique())
    df_cycle['Label'] = ['MP-Cycling'] * len(df_cycle['SegmentName']) # .str.contains('PD_PostPEC')

    writer = pd.ExcelWriter(os.path.join(des_path, 'Perf.xlsx'), engine='xlsxwriter')
    get_pivot_table(df_cycle, 'tERS', plane_type + '-Cycling', 'tErase', writer)
    df_cycle = df_cycle[df_cycle['Level']!='SLC']
    df_cycle = df_cycle[df_cycle['Level']!='MLC']
    get_pivot_table(df_cycle, 'tPROG', plane_type + '-Cycling', 'tProg', writer)

    df_source = df_cycle[(df_cycle['Function']=='MP-READ') | (df_cycle['Function']=='READ-MP')].copy()
    get_pivot_table(df_source, 'tR', 'MP-Cycling', 'tRead', writer)
    writer.close()
# *************************** End: get_performance *************************** #

def perf_excel_to_write(wb, ws, ws_to_write, start_row_index=1, end_row_index=1, get_col_index=1, write_row_index=2, operation='tErase', Type='Min'):
    # sheet_lis = wb.sheetnames
    # print(sheet_lis)
    data = dict()
    for row in range(start_row_index, end_row_index):
        temperature = ws.cell(row=row, column=2).value
        value = ws.cell(row=row, column=get_col_index).value
        value = round(value, 0)
        data[temperature] = value

    # row_count = ws_to_write.max_row
    row_idx = write_row_index
    if operation == 'tErase':
        write_col_index = 4
    elif operation == 'tProg':
        write_col_index = 5
    elif operation == 'tRead':
        write_col_index = 6

    for dict_key, dict_value in data.items():
        
        ws_to_write.cell(row=row_idx, column=1, value=Type)
        ws_to_write.cell(row=row_idx, column=2, value=dict_key)
        ws_to_write.cell(row=row_idx, column=3, value=nand_dev)
        ws_to_write.cell(row=row_idx, column=write_col_index, value=dict_value)

        row_idx = row_idx + 1
    

def power_excel_to_write(wb, ws, ws_to_write, start_row_index=1, end_row_index=1, get_col_index=1, write_row_index=2, sheet_for_power='Power', type_item='Prog_VCC Avg (mW)'):
    # sheet_lis = wb.sheetnames
    # print(sheet_lis)
    data = dict()
    for row in range(start_row_index, end_row_index):
        temperature = ws.cell(row=row, column=get_col_index-1).value
        value = ws.cell(row=row, column=get_col_index).value
        # print('value: ', value)
        value = round(value, 1)
        data[temperature] = value

    if 'ERS' in sheet_for_power:
        index_offset = 8
    else:
        index_offset = 0

    row_idx = write_row_index
    if type_item == 'VCC Avg (mW)':
        write_col_index = 3 + index_offset
    elif type_item == 'VCC Max (mW)':
        write_col_index = 4 + index_offset
    elif type_item == 'VCCQ Avg (mW)':
        write_col_index = 5 + index_offset
    elif type_item == 'VCCQ Max (mW)':
        write_col_index = 6 + index_offset
    elif type_item == 'Data-transfer_VCC Avg (mW)':
        write_col_index = 7
    elif type_item == 'Data-transfer_VCC Max (mW)':
        write_col_index = 8
    elif type_item == 'Data-transfer_VCCQ Avg (mW)':
        write_col_index = 9
    elif type_item == 'Data-transfer_VCCQ Max (mW)':
        write_col_index = 10

    for dict_key, dict_value in data.items():
        ws_to_write.cell(row=row_idx, column=1, value=dict_key)
        ws_to_write.cell(row=row_idx, column=2, value=nand_dev)
        ws_to_write.cell(row=row_idx, column=write_col_index, value=dict_value)
        row_idx = row_idx + 1
    
def perf_summary(base_path):
    wb = openpyxl.load_workbook(os.path.join(base_path, 'Perf.xlsx'))
    ws_to_write = wb.create_sheet('Summary', 0)
    items = ['Type', 'Temp', 'NAND', 'tBER [us]', 'tPROG [us]', 'tREAD [us]']
    for i in range(len(items)):
        ws_to_write.cell(row=1, column=i+1, value=items[i])
    
    write_row_index = 2
    for type_item in ['Min', 'Avg', 'Max']:
        if type_item == 'Min':
            get_col_index = 4
        elif type_item == 'Avg':
            get_col_index = 5
        elif type_item == 'Max':
            get_col_index = 6

        for operation in ['tErase', 'tProg', 'tRead']:
            ws = wb[operation]
            perf_excel_to_write(wb, ws, ws_to_write, start_row_index=2, end_row_index=5, get_col_index=get_col_index, write_row_index=write_row_index, operation=operation, Type=type_item)

        write_row_index = write_row_index + 3
    
    wb.save(os.path.join(base_path, 'Perf.xlsx'))


def power_summary(power_path, wb_to_write, ws_to_write, file_list):
    
    # wb_to_write = openpyxl.Workbook()
    # ws_to_write = wb_to_write.active
    # ws_to_write.title = 'Summary'
    # ws_to_write = wb_to_write.create_sheet('Summary', 0)

    print('file_list:', file_list)
    if 'READ' in file_list[0]: 
        items = ['Temp', 'NAND', 'Read_VCC Avg (mW)', 'Read_VCC Max (mW)', 'Read_VCCQ Avg (mW)', 'Read_VCCQ Max (mW)', 'Read_Data-out_VCC Avg (mW)', 'Read_Data-out_VCC Max (mW)', 'Read_Data-out_VCCQ Avg (mW)', 'Read_Data-out_VCCQ Max (mW)']
    else:
        items = ['Temp', 'NAND', 'Prog_VCC Avg (mW)', 'Prog_VCC Max (mW)', 'Prog_VCCQ Avg (mW)', 'Prog_VCCQ Max (mW)', 'Prog_Data-in_VCC Avg (mW)', 'Prog_Data-in_VCC Max (mW)', 'Prog_Data-in_VCCQ Avg (mW)', 'Prog_Data-in_VCCQ Max (mW)', 'Erase_VCC Avg (mW)', 'Erase_VCC Max (mW)', 'Erase_VCCQ Avg (mW)', 'Erase_VCCQ Max (mW)']
    
    for i in range(len(items)):
        ws_to_write.cell(row=1, column=i+1, value=items[i])

    for power_file in file_list:
        wb = openpyxl.load_workbook(os.path.join(power_path, power_file))
        
        # if 'PRG' in power_file:
        #     worksheet_list = ['MP-PRG_Power', 'MP-PRG_Datatransfer_Power']
        # elif 'ERS' in power_file:
        #     worksheet_list = ['MP-ERS_Power']
        # elif 'MP-READ_Power' in power_file:
        #     worksheet_list = ['MP-READ_Power_ALL', 'MP-READ_Datatransfer_Power_ALL']
        # elif 'SP-READ_Power' in power_file:
        #     worksheet_list = ['READ_Power_ALL', 'READ_Datatransfer_Power_ALL']

        worksheet_list = wb.sheetnames
  
        for worksheet in worksheet_list:
            if ('READ' not in worksheet) or ('READ' in worksheet and 'ALL' in worksheet):
                print('worksheet: ', worksheet)
                ws = wb[worksheet]
                if 'Datatransfer' not in worksheet:
                    items = ['VCC Avg (mW)', 'VCC Max (mW)', 'VCCQ Avg (mW)', 'VCCQ Max (mW)']
                else:
                    items = ['Data-transfer_VCC Avg (mW)', 'Data-transfer_VCC Max (mW)', 'Data-transfer_VCCQ Avg (mW)', 'Data-transfer_VCCQ Max (mW)']

                for type_item in items:
                    write_row_index = 2
                    if type_item == 'VCC Avg (mW)' or type_item == 'Data-transfer_VCC Avg (mW)':
                        start_row_index = 2
                        get_col_index = 3
                    elif type_item == 'VCC Max (mW)' or type_item == 'Data-transfer_VCC Max (mW)':
                        start_row_index = 7
                        get_col_index = 3
                    elif type_item == 'VCCQ Avg (mW)' or type_item == 'Data-transfer_VCCQ Avg (mW)':
                        start_row_index = 2
                        get_col_index = 10
                    elif type_item == 'VCCQ Max (mW)' or type_item == 'Data-transfer_VCCQ Max (mW)':
                        start_row_index = 7
                        get_col_index = 10

                    power_excel_to_write(wb, ws, ws_to_write, start_row_index=start_row_index, end_row_index=start_row_index+3, get_col_index=get_col_index, write_row_index=write_row_index, sheet_for_power=worksheet, type_item=type_item)

    width = 3  # 手动加宽的数值 
    dims = {}
    for row in ws_to_write.rows:
        for cell in row:
            if cell.value:
                cell_len = 0.7 * len(re.findall('([\u4e00-\u9fa5])', str(cell.value))) + len(str(cell.value))
                dims[cell.column] = max((dims.get(cell.column, 0), cell_len))
        for col, value in dims.items():
            # ws_to_write.column_dimensions[col].width = value + width 
            col_letter = get_column_letter(col)  # 将整数列索引转换为字母
            ws_to_write.column_dimensions[col_letter].width = value + width

    max_rows = ws_to_write.max_row  # 获取最大行
    max_columns = ws_to_write.max_column  # 获取最大列
    align = Alignment(horizontal='center', vertical='center')
    for i in range(1, max_rows + 1):
        for j in range(1, max_columns + 1):
            ws_to_write.cell(row=i, column=j).alignment = align

    wb.save(os.path.join(power_path, power_file))
    wb_to_write.save(os.path.join(power_path, 'Summary_Power.xlsx'))

def blk2pec(x):
    x = str(x)
    if x in PEC_0K:
        return 1
    elif x in PEC_3K:
        return 1
    elif x in PEC_7K:
        return 1
    # elif x in PEC_10K:
    #     return 10000
    else:
        return 0

def pec(block):
    blk_list = dict()
    for i in range(len(block)):
        blk_group = []
        first = int((block[i].split('-')[0]))
        last = int((block[i].split('-')[1]))+1

        for j in range(num_blk_per_pec):
            start = first + j * blk_freq
            end = last + j * blk_freq

            for blk in range(start, end):
                blk_group.append(int(blk))

        blk_list['group'+str(i)] = blk_group

    PEC_0K = blk_list['group0']
    PEC_3K = blk_list['group1']
    PEC_7K = blk_list['group2']
    # PEC_10K = blk_list['group3']

    print('PEC_0K:', PEC_0K, '; PEC_3K:', PEC_3K, '; PEC_7K:', PEC_7K)
    return PEC_0K, PEC_3K, PEC_7K

if __name__ == '__main__':

    nand_dev ='X3-9070'
    if 'B4' in nand_dev:
        base_path = r'X:\NAND_Repository\NAND\2_NAND_Data\2_Timing & Power\WDC\BiCS4_1TB_HDP_7K\WDC_BiCS4_2P_HDP_512Gb_800MT_2blks\20210521' # BiCS5
        block = ['0-1', '20-21', '30-31'] # BiCS4

    elif 'B5' in nand_dev:
        base_path = r'D:\0_Projects\NplusT\2_BiCS5X3_1Tb\1_Performance' # BiCS5
        block = ['0-3', '200-203', '400-403'] # BiCS5
    
    elif 'B6' in nand_dev:
        base_path = r'D:\0_Projects\NplusT\0_BiCS6X3_512Gb(QS)\1_Performance' # BiCS6
        block = ['0-3', '240-243', '400-403'] # BiCS6
    
    elif 'B8' in nand_dev:
        base_path = r'D:\0_Projects\NplusT\1_BiCS8X3_1Tb(QS)\1_Performance' # BiCS8
        block = ['0-3', '200-203', '440-443'] # BiCS8

    elif 'X2-9060' in nand_dev:
        base_path = r'X:\NAND_Repository\NAND\2_NAND_Data\2_Timing & Power\YMTC\X29060_512GB_ODP_7K\X29060_4P_ODP_512Gb_1200MT_30C_55C_75C_1CH_1Die_Vcc=2.5V_Vccq=1.2V\MP&SP' # X2-9060
        
        # base_path = r'X:\NAND_Repository\NAND\2_NAND_Data\2_Timing & Power\YMTC\X29060_512GB_ODP_7K\X29060_4P_ODP_512Gb_1200MT_30C_55C_75C_1CH_1Die_Vcc=2.5V_Vccq=1.2V\MP'
        block = ['0-3', '20-23', '300-303'] # YMTC X29060
    elif 'X3-9070' in nand_dev:
        base_path = r'D:\0_Projects\NplusT\3_YMTCX39070\1_Perf\ODT4_DriveStrength2_WriteReadLatency44' # X3-9070
        
        # base_path = r'X:\NAND_Repository\NAND\2_NAND_Data\2_Timing & Power\YMTC\X29060_512GB_ODP_7K\X29060_4P_ODP_512Gb_1200MT_30C_55C_75C_1CH_1Die_Vcc=2.5V_Vccq=1.2V\MP'
        block = ['0-3', '200-203', '440-443'] # YMTC X39070

    # for file in os.listdir(os.path.join(base_path, 'raw_data')):
    #     if 'HS' in file and 'csv' in file:
    #         file_path = os.path.join(base_path, 'raw_data', file)

    #hang
    for file in os.listdir(base_path):
        if 'HS' in file and 'csv' in file:
            file_path = os.path.join(base_path, file)

    des_path = base_path

    Vcc, Vccq = 3.3, 1.2
    # Vcc, Vccq = 1, 1
    blk_freq = 40
    num_blk_per_pec = 1

    PEC_0K, PEC_3K, PEC_7K = pec(block)

    savefile_prog = des_path + r'\tProg.csv'
    savefile_erase = des_path + r'\tErase.csv'
    savefile_read = des_path + r'\tRead.csv'

    if not os.path.exists(des_path):
        os.makedirs(des_path)

    # for file in os.listdir(des_path):
    #     if os.path.join(des_path,file).endswith('.csv'):
    #         file_path = os.path.join(des_path, file)
    #         os.remove(file_path)

    get_performance('MP')
    perf_summary(base_path)
        
    get_power()
    power_path = base_path
    wb_to_write = openpyxl.Workbook()
    ws_to_write = wb_to_write.active

    ws_to_write.title = 'Summary_MP_EP'

    ep_excel_list, mp_read_list, sp_read_list = [], [], []
    for file in os.listdir(base_path):
        if 'PRG' in file or 'ERS' in file:
            ep_excel_list.append(file)
        elif 'MP' in file and 'READ' in file:
            mp_read_list.append(file)
        elif 'SP' in file and 'READ' in file:
            sp_read_list.append(file)

    if ep_excel_list:
        power_summary(power_path, wb_to_write, ws_to_write, ep_excel_list)

    if mp_read_list:
        ws_to_write = wb_to_write.create_sheet('Summary_MP_Read', 1)
        power_summary(power_path, wb_to_write, ws_to_write, mp_read_list)

    if sp_read_list:
        ws_to_write = wb_to_write.create_sheet('Summary_SP_Read', 2)
        power_summary(power_path, wb_to_write, ws_to_write, sp_read_list)
    
    