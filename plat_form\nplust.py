import re
import csv
import gc
import os
import math
import time
import shutil
import threading
import numpy as np
import pandas as pd
#from weights import quantile_1D, quantile
from scipy.stats import norm
from numba import njit, prange
from scipy.optimize import curve_fit
from alive_progress import alive_bar

import nandinfo as ndinfo
from function.hit_rate import *
from function.process_data import *
from device.device_configure import *
from colorama import init, Fore, Back, Style
init(autoreset=True)


def Partial_Config(df, open_block):

    Prog = ' Partially Prog'

    for b in range(len(open_block)):
        if ("All" in partial_prog_ratio.keys()): #add by hang
            Partail_Ratio = partial_prog_ratio['All'][b]
            df_temp = df[df.Block.isin(open_block[b])].copy()
            df_temp['Type'] = Partail_Ratio + Prog
            df = df[~(df.Block.isin(open_block[b]))]
            df = pd.concat([df, df_temp], ignore_index=True)
            del df_temp #hang, free memory
        else:
            for chp_ce_lun in partial_prog_ratio.keys():  #test_partial_prog_ratio -> partial_prog_ratio

                Partail_Ratio = partial_prog_ratio[chp_ce_lun][b]
                Wordline = partial_prog_wl[Partail_Ratio] #partial_prog_wl[chp_ce_lun][b] -> partial_prog_wl[Partail_Ratio]

                # df_temp = df[(df.Block.isin(open_block[b])) & (df['WordLine']<=Wordline) & (df['CHP_CE_LUN']==chp_ce_lun)].copy()
                df_temp = df[(df.Block.isin(open_block[b])) & (df['CHP_CE_LUN']==chp_ce_lun)].copy()
                df_temp.loc[:, 'Type'] = Partail_Ratio + Prog
                df = df[~(df.Block.isin(open_block[b]) & (df['CHP_CE_LUN']==chp_ce_lun))]
                df = pd.concat([df, df_temp], ignore_index=True)
                # df = df.append(df_temp)
                del df_temp #hang, free memory
    return df


'''
# if 'ReadOut' in df_filter['Read'].unique():
    #     df_filter.loc[df_filter.Read == 'ReadOut', 'Read'] = 'Default Read'
## df_readout = df_filter[(df_filter['Function']=='MP-READ')].copy()
## df_readout.loc[:,'Read'] = ['Default Read']*len(df_readout['Function']=='MP-READ')

# df_readout = df_filter[(df_filter['Read']=='Default Read')].copy() 
# df_readout.loc[:,'Condition'] = ['0x0'] * len(df_readout['Read']=='Default Read')

# df_readretry = df_filter[(df_filter['Read']==retry_mode)].copy()
# df_init = df_readout.append(df_readretry)
# if os.path.join(store_dir, csv_folder).endswith('.csv'):
'''


def first_read(df_init):
    try:
        # df_init.loc[df_init['SegmentName'].str.contains('1st'), 'First_Read'] = '1st Read'

        # df_init.loc[~((df_init['SegmentName'].str.contains('2nd')) | (df_init['Condition'].str.contains('RR'))), 'First_Read'] = '1st Read'

        # df_init.loc[df_init['SegmentName'].str.contains('2nd'), 'First_Read'] = '2nd Read'

        #add by hang
        df_init.loc[df_init['Read']=='Default Read', 'First_Read'] = '1st Read'
        df_init.loc[((df_init['Read']=='Read Retry') & (df_init['Condition'].str.contains('RR0'))), 'First_Read'] = '2nd Read'
        
        if (('Micron' in ndinfo.device) or ('B47R' in ndinfo.device)) and 'DR' in ndinfo.test_for:
            df_init.loc[df_init['Read']=='Default Read', 'First_Read'] = '1st Read'
            df_init.loc[df_init['Condition'].str.contains('RR0'), 'First_Read'] = '2nd Read'

        return df_init
        
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def add_item(grp_cols, list_item):
    if list_item:
        for key_item in list_item:
            if key_item in grp_cols:
                grp_cols = grp_cols
            else:
                grp_cols.append(key_item)
    return grp_cols

def Extracting(store_dir, df_init, pids_list, suffix_name):
    try:
        grp_cols = ['Block','Level','Condition','Type','Read', ndinfo.test_stress,'TestTemp'] + [die_locator]
        grp_cols = add_item(grp_cols, list_item)
                
        if 'DR'.upper() in test_for.upper():
            grp_cols.extend(['First_Read'])
        
        df_init.rename(columns={'Value':'FBC', 'Label':'Read'}, inplace=True)
        df_init.loc[:, 'Type'] = 'Fully Prog'
        if 'Partial' in ndinfo.test_for:
            start_time = time.time()
            
            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                print('partial-config start at ', datetime.now()) 

            open_block = []
            for len_p in range(test_num_blk_per_pec):
                # if 'X36070' in device:
                #     step = {#0K, 1K, 2K, 3K, 4K, 5K
                #             0: [0,0,0,0,0,0],
                #             1: [132,132,132,116,116,116],
                #             2: [264,264,264,248,248,248],
                #             3: [396,396,396,380,380,380]}
                #     open_block.append(get_block_value_nplust_X36070(test_base_blk, step[len_p], pids_list))
                # else:
                #     open_block.append(get_block_value_nplust(ndinfo.base_blk, blk_freq * len_p, pids_list))
                open_block.append(get_block_value_nplust(ndinfo.base_blk, blk_freq * len_p, pids_list))
            df_init = Partial_Config(df_init, open_block)
            end_time = time.time()
            
            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                print('Elapsed for Partial Config: {0:0.2f}min'.format((end_time - start_time)/60))

        df_init['FBC'].replace('', np.nan, inplace=True) #将空值替换成na
        df_init.dropna(axis=0, how='any', inplace=True) #删除含有na的行
        df_init['Condition'] = df_init['Condition'].apply(lambda x: 'RR' + str(int(x.split('0x')[1], 16))) #int函数中的16代表前面的数字是16进制的，转换成10进制输出
        # if list_item:
        #     for col_id in range(len(list_item)):
                # df_init[list_item[col_id]] = add_col[col_id]    #hang, 以后如果有用到要添加的固定值，可以在这里uncomment
        
        if 'DR'.upper() in test_for.upper():
            # df_init['First_Read'] = 'None'
            df_init['First_Read'] = 'Not1st2nd' #hang, 2024.10.28修改
            df_init = first_read(df_init)   #hang, defaultread -> 1st read, readretry&RR0 -> 2nd read, other readretry -> 20241018
        # df_init['PD_temp_Read_temp'] = df_init['Temperature'].astype(str)  + '_' + df_init['Temperature'].astype(str)
        df_init['CW'] = df_init.groupby(grp_cols + ['WordLine', 'Page']).cumcount() #为每个block的每个wordline的每个page的每个cycle的每个codeword添加一个递增的序号
        df_init.reset_index(drop=True)

        if 0:
            df_blk_merge = df_init[['FBC', 'Cycle'] + grp_cols].value_counts().reset_index(name='Count').sort_values('FBC')
            df_blk_merge['BER'] = df_blk_merge['FBC'] / (codeword*8)

        def write_csv(store_dir):
            df_init.to_csv(store_dir + r'/Initialization_data_' + suffix_name + '.csv', index=False, header=1, mode='w+')
            # df_blk_merge.to_csv(store_dir + r'/Data_by_blk_' + suffix_name + '.csv', index=False, header=1, mode='w+')

        threading.Thread(target=write_csv, args={store_dir}).start()     #hang, comment for saving memory and time 

        print(Fore.RED + 'Initialization {} end.'.format(suffix_name))  #hang, debug

        return df_init
        # return df_init, df_blk_merge

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
    
# *************************** End: Initialization and Block files: Get SFR and Merge RR based on block *************************** #


#**************************************************************************************************************************#
def cell_type(mode, x, device):
    try:
        if mode == 'TLC':
            if 'Micron' in device or 'B47R' in device:
                if x < 4 or x >= 2108:
                    return 'SLC'
                if x <= 1063 and x >= 1048:
                    return 'MLC'
                else:
                    return 'TLC'
            else:
                return 'TLC'
        elif mode == 'QLC': #chang for X36070, hang
            if 'X36070' in device:
                if x >= 5520:
                    return 'SLC'
                elif (18 <= x and x <= 2705) or (2778 <= x and x <= 5465):
                    return 'QLC'
                else:
                    return 'TLC'
            else:
                return 'QLC'

        else:
            return 'SLC'

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

  
def page2wl(x):
    try:
        if 'B47R'.upper() in device or 'Micron'.upper() in device:
            if x <4: # SLC
                return int(x)
            elif x >= 1048 and x <= 1063: # MLC
                return int((x-1048)//2+352)
            elif x >= 2108: # SLC
                return int(x-1400)
            elif x > 1063 and x < 2108: # TLC
                return int((x+16)//bits_per_cell)
            else: # TLC
                return int((x+8)//bits_per_cell)
        elif "X36070" in device:
            if x <18: # TLC
                return int(x//3)
            elif x >= 18 and x <= 2705: # QLC
                return int((x-18)//4+6)
            elif x >= 2706 and x <= 2777: # TLC
                return int((x-2706)//3+678)
            elif x >= 2778 and x <= 5465: # QLC
                return int((x-2778)//4+702)
            elif x >= 5466 and x <= 5519: # TLC
                return int((x-5466)//3+1374)
            else: # SLC
                return int((x-5520)+1392)

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())


def pec(x):
    x = int(x)
    #change for X36070, hang
    if "X36070" in device:
        if x in PEC_0K:
            return 1
        elif x in PEC_1K:
            return 1000
        elif x in PEC_2K:
            return 2000
        elif x in PEC_3K:
            return 3000
        elif x in PEC_4K:
            return 4000
        elif x in PEC_5K:
            return 5000
        else:
            return 1
    else:
        if x in PEC_0K:
            return 1
        elif x in PEC_3K:
            return 3000
        elif x in PEC_7K:
            return 7000
        elif x in PEC_10K:
            return 10000
        else:
            return 1

def parse_multi_list(block_str, pids_list):
    block_list = []

    while True:
        start = block_str.find("[")
        end = block_str.find("]", start)
        if start == -1:
            break
        if end == -1:
            break

        block = block_str[start + 1: end]
        block_list.append(block)
        
        start = end + 1
        block_str = block_str[start:len(block_str)]
        if len(block_str) == 0:
            break

    if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
        print(Fore.BLUE + '   [' + str(datetime.now()) + '] PEC <---> Block used')
        print('        Block for 0K, 3K, 7K, 10K: ', block_list)
    return block_list

def block2pec(block_str, pids_list):
    try:
        # Change for X36070, hang
        global PEC_0K, PEC_1K, PEC_2K, PEC_3K, PEC_4K, PEC_5K, PEC_7K, PEC_10K
        if "X36070" in device:
            # global PEC_0K, PEC_1K, PEC_2K, PEC_3K, PEC_4K, PEC_5K
            block_list = parse_multi_list(block_str, pids_list)
            blk_dict = dict()
            # step = {#0K, 1K, 2K, 3K, 4K, 5K
            #         0: [0,0,0,0,0,0],
            #         1: [132,132,132,116,116,116],
            #         2: [264,264,264,248,248,248],
            #         3: [396,396,396,380,380,380]}
            for i in range(len(block_list)):
                blk_group = []
                first = int((block_list[i].split('-')[0]))
                last = int((block_list[i].split('-')[1]))+1

                for j in range(num_blk_per_pec):
                    start = first + j * blk_freq
                    end = last + j * blk_freq
                    # start = first + step[j][i]
                    # end = last + step[j][i]
                    blk_group.extend([int(blk) for blk in range(start, end)])

                blk_dict['group'+str(i)] = blk_group

            PEC_0K = blk_dict['group0']
            PEC_1K = blk_dict['group1']
            PEC_2K = blk_dict['group2']
            PEC_3K = blk_dict['group3']
            PEC_4K = blk_dict['group4']
            PEC_5K = blk_dict['group5']

            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                print('        PEC_0K:  {0}'.format(PEC_0K))
                print('        PEC_1K:  {0}'.format(PEC_1K))
                print('        PEC_2K:  {0}'.format(PEC_2K))
                print('        PEC_3K:  {0}'.format(PEC_3K))
                print('        PEC_4K:  {0}'.format(PEC_4K))
                print('        PEC_5K:  {0}'.format(PEC_5K))
                # print()
            return PEC_0K, PEC_1K, PEC_2K, PEC_3K, PEC_4K, PEC_5K
        else:
            # global PEC_0K, PEC_3K, PEC_7K, PEC_10K
            block_list = parse_multi_list(block_str, pids_list)
            blk_dict = dict()
            for i in range(len(block_list)):
                blk_group = []
                first = int((block_list[i].split('-')[0]))
                last = int((block_list[i].split('-')[1]))+1

                for j in range(num_blk_per_pec):
                    start = first + j * blk_freq
                    end = last + j * blk_freq
                    blk_group.extend([int(blk) for blk in range(start, end)])

                blk_dict['group'+str(i)] = blk_group

            PEC_0K = blk_dict['group0']
            PEC_3K = blk_dict['group1']
            PEC_7K = blk_dict['group2']
            PEC_10K = blk_dict['group3']

            if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                print('        PEC_0K:  {0}'.format(PEC_0K))
                print('        PEC_3K:  {0}'.format(PEC_3K))
                print('        PEC_7K:  {0}'.format(PEC_7K))
                print('        PEC_10K: {0}'.format(PEC_10K))
                # print()
            return PEC_0K, PEC_3K, PEC_7K, PEC_10K

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())


def get_block_value_nplust(base_block, step, pids_list):
    try:
        blk_group = []
        for pec in base_block.keys():
            for p in range(plane):
                blk_group.append(base_block[pec] + step + p)

        if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
            print('Open Block: ', blk_group)
        return blk_group

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def get_block_value_nplust_X36070(base_block, step:[], pids_list):
    try:
        blk_group = []
        for pec in base_block.keys():
            current_step = step[list(base_block.keys()).index(pec)]
            for p in range(plane):
                blk_group.append(base_block[pec] + current_step + p)

        if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
            print('Open Block: ', blk_group)
        return blk_group

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())


def NAND_CFG(df, device, mode):
    try:
        # Change for X36070, hang
        if 'B47R'.upper() in device or 'Micron'.upper() in device or "X36070" in device:
            df['WordLine'] = df['Page'].apply(lambda x: page2wl(int(x))) 
            df['Cell'] = df['Page'].apply(lambda x: cell_type(mode, x, device))       
        else:
            df['WordLine'] = df['Page'].apply(lambda x: int(int(x)//bits_per_cell))
    
        df['Level'] = df['Page'].apply(lambda x: level_func(device, mode, int(x)))
        df['Cycle'] = df['Block'].apply(lambda x: pec(x))
        df['Layer'] = df['WordLine'].apply(lambda x: int(x // wl_per_layer))

        return df

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
#*******************************************************************************************

def init_nplust(folder_path, pids_list, store_dir, suffix_name):
    try:
        device = ndinfo.device
        block = ndinfo.block
        mode = ndinfo.mode
        
        start_time = time.time()
        # Change for X36070, hang
        if "X36070" in device:
            PEC_0K, PEC_1K, PEC_2K, PEC_3K, PEC_4K, PEC_5K = block2pec(block, pids_list)    #block = [4-7][12-15][20-23][44-47][52-55][64-67]
        else:
            PEC_0K, PEC_3K, PEC_7K, PEC_10K = block2pec(block, pids_list) 

        # print('NplusT-0:', pids_list[-1], ', ', pids_list, ', ', id(pids_list))
        for file_csv in os.listdir(folder_path):
            if 'csv' in file_csv:
                file = os.path.join(folder_path, file_csv)
                df_init = pd.read_csv(file, low_memory=False)

                if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                    print(Fore.BLUE + '   [' + str(datetime.now()) + '] Raw File Opened...')

                group_lst = ['SegmentName','Label', 'Condition', 'CHP_CE_LUN', 'Block', 'Page', 'Value', ndinfo.test_stress,'TestTemp'] #hang
                group_lst = add_item(group_lst, list_item) #hang

                # df_init = df_init.loc[:, ['SegmentName','Label', 'Condition', 'CHP_CE_LUN', 'Block', 'Page', 'Value', ndinfo.test_stress]]
                df_init = df_init.loc[:, group_lst] #hang
                
                df_init = NAND_CFG(df_init, device, mode) #计算wordline,level,cycle,layer
                df_init_csv = Extracting(store_dir, df_init, pids_list, suffix_name)    # Initial data is output to "Outcome/subfolder"

                if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
                    print(Fore.BLUE + '   [' + str(datetime.now()) + '] NAND Configure/Extracting Done...')

                del df_init; #hang

        end_time = time.time()
        if pids_list[-1] == str(os.getpid()): # or float(ratio) == 1:
            print(Fore.BLUE + '   [' + str(datetime.now()) + '] Elapsed of Initial File: {0:0.2f}min'.format((end_time - start_time)/60))

        return df_init_csv #, df_blk_csv

    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())