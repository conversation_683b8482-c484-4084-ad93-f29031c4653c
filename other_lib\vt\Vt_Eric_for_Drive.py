import csv
import os
import re
import math
from pickle import NONE
import shutil
import time
#from weights import quantile_1D, quantile
from scipy.stats import norm
from scipy.optimize import curve_fit
import pandas as pd
import numpy as np
# from progress.bar import Bar
from alive_progress import alive_bar

### ***************************** Below: Modify regarding testing ***************************** ###  
path = r'D:\1_Personal\0_Materials\5_Script\Raw_Data_for_BlkRD'
device = 'X29060' # B47R
mode = 'TLC' # 'WDC TLC'; 'Micron TLC'
Test_for = 'BlkRD' # Partial+BlkRD; Partial+SPRD; Partial Prog;SPRD; BlkRD

def vt_merge(path):
    for file in os.listdir(path):
        if 'vt_for' in file:
            print(file)
            data_vt = pd.read_csv(os.path.join(path, file, 'Vt_initial_data.csv'))

            if not os.path.exists(path+r'\combined_vt.csv'):
                data_vt.to_csv(path+r'\combined_vt.csv', index=False, header=True, mode='a+')
            else:
                data_vt.to_csv(path+r'\combined_vt.csv', index=False, header=False, mode='a+')

# def rd_title(block):
#     if int(block) in [10, 50, 90]: # [10, 50, 90] [31, 131, 231]
#         name = '_every3minsRD'
#     elif int(block) in [15, 55, 95]: # [15, 55, 95] [45, 145, 245]
#         name = '_every1hrsRD'
#     elif int(block) in [20, 60, 100]: # [60, 160, 260]
#         name = '_noRD'
#     else:
#         name = ''
#     return name

def rd_title(block, old_version):
    old_version = False
    blk_set1, blk_set2 = True, True
    page_level_0 = 'all'
    page_level_1 = '22-sampled good wl'
    page_level_2 = 'seq-selected wl'
    name, result = '', True

    if old_version:
        page_level_0 = 'lsb+csb'
        page_level_1 = 'lastwlmsb'
        page_level_2 = 'lastwlcsb'
    else:
        page_level_0 = 'all'
        page_level_1 = '22-sampled good wl'
        page_level_2 = 'seq-selected wl'

    if blk_set1:

        wl_delay = 'WL-Delay:100ms & '
        if old_version:
            block_delay_list = ['Blk-Delay:10hrs', 'Blk-Delay:6hrs', 'Blk-Delay:2hrs', 'Blk-Delay:4hrs']
        else:
            block_delay_list = ['Blk-Delay:10hrs', 'Blk-Delay:6hrs', 'Blk-Delay:2hrs', 'Blk-Delay:4hrs']

        suffix_list = [' after scan whole wls', ' after split wls by 6']

        if int(block) == 10:
            name = wl_delay + block_delay_list[0] + suffix_list[0] + '_' + 'lsb+csb'
        elif int(block) == 90:
            name = wl_delay + block_delay_list[0] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 170:
            name = wl_delay + block_delay_list[0] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 15:
            name = wl_delay + block_delay_list[1] + suffix_list[0] + '_' + 'lsb+csb'
        elif int(block) == 95:
            name = wl_delay + block_delay_list[1] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 175:
            name = wl_delay + block_delay_list[1] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 20:
            name = wl_delay + block_delay_list[2] + suffix_list[0] + '_' + 'lsb+csb'
        elif int(block) == 100:
            name = wl_delay + block_delay_list[2] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 180:
            name = wl_delay + block_delay_list[2] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 25:
            name = wl_delay + block_delay_list[3] + suffix_list[0] + '_' + 'lsb+csb'
        elif int(block) == 105:
            name = wl_delay + block_delay_list[3] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 185:
            name = wl_delay + block_delay_list[3] + suffix_list[1] + '_' + page_level_2

        else:
            result = False

    if result == False:
        wl_delay_list = ['WL-Delay:100ms & ', 'WL-Delay:300ms & ']
        block_delay_list = ['Blk-Delay:2hrs', 'Blk-Delay:4hrs']
        suffix = ' after old scan 6layers'

        if int(block) == 190:
            name = wl_delay_list[0] + block_delay_list[0] + suffix + '_' + 'csb/msb'
            result = True
        elif int(block) == 195:
            name = wl_delay_list[1] + block_delay_list[0] + suffix + '_' + 'csb/msb'
            result = True
        elif int(block) == 200:
            name = wl_delay_list[0] + block_delay_list[1] + suffix + '_' + 'csb/msb'
            result = True
        elif int(block) == 205:
            name = wl_delay_list[1] + block_delay_list[1] + suffix + '_' + 'csb/msb'
            result = True
        else:
            result = False

        if result == False:
            wl_delay_list = ['WL-Delay:100ms & ', 'WL-Delay:300ms & ']
            block_delay_list = ['Blk-Delay:2hrs', 'Blk-Delay:4hrs']
            suffix = ' after new scan 20layers'
            if int(block) == 210:
                name = wl_delay_list[0] + block_delay_list[0] + suffix + '_' + 'csb/msb'
                result = True
            elif int(block) == 215:
                name = wl_delay_list[1] + block_delay_list[0] + suffix + '_' + 'csb/msb'
                result = True
            elif int(block) == 220:
                name = wl_delay_list[0] + block_delay_list[1] + suffix + '_' + 'csb/msb'
                result = True
            elif int(block) == 225:
                name = wl_delay_list[1] + block_delay_list[1] + suffix + '_' + 'csb/msb'
                result = True
            else:
                result = False

    if blk_set2 and result==False:
        wl_delay_list = ['WL-Delay:300ms & ', 'WL-Delay:100ms & ']
        if old_version:
            block_delay_list = ['Blk-Delay:10hrs', 'Blk-Delay:6hrs', 'Blk-Delay:4hrs', 'Blk-Delay:2hrs']
        else:
            block_delay_list = ['Blk-Delay:10hrs', 'Blk-Delay:6hrs', 'Blk-Delay:4hrs', 'Blk-Delay:2hrs']

        suffix_list = [' after split wls by 6', ' after split wls by 3']

        if int(block) == 30:
            name = wl_delay_list[0] + block_delay_list[0] + suffix_list[0] + '_' + page_level_1
        elif int(block) == 110:
            name = wl_delay_list[0] + block_delay_list[0] + suffix_list[0] + '_' + page_level_2


        elif int(block) == 35:
            name = wl_delay_list[0] + block_delay_list[1] + suffix_list[0] + '_' + page_level_1
        elif int(block) == 115:
            name = wl_delay_list[0] + block_delay_list[1] + suffix_list[0] + '_' + page_level_2


        elif int(block) == 40:
            name = wl_delay_list[0] + block_delay_list[2] + suffix_list[0] + '_' + page_level_1
        elif int(block) == 120:
            name = wl_delay_list[0] + block_delay_list[2] + suffix_list[0] + '_' + page_level_2


        elif int(block) == 45:
            name = wl_delay_list[0] + block_delay_list[3] + suffix_list[0] + '_' + page_level_1

        elif int(block) == 125:
            name = wl_delay_list[0] + block_delay_list[3] + suffix_list[0] + '_' + page_level_2


        elif int(block) == 50:
            name = wl_delay_list[1] + block_delay_list[0] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 130:
            name = wl_delay_list[1] + block_delay_list[0] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 55:
            name = wl_delay_list[1] + block_delay_list[1] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 135:
            name = wl_delay_list[1] + block_delay_list[1] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 60:
            name = wl_delay_list[1] + block_delay_list[2] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 140:
            name = wl_delay_list[1] + block_delay_list[2] + suffix_list[1] + '_' + page_level_2


        elif int(block) == 65:
            name = wl_delay_list[1] + block_delay_list[3] + suffix_list[1] + '_' + page_level_1
        elif int(block) == 145:
            name = wl_delay_list[1] + block_delay_list[3] + suffix_list[1] + '_' + page_level_2

        if name:
            result = True
        else:
            result = False


    return name

def find_pattern(folder_path, file_pattern, old_version):
    for root, dirs, files in os.walk(folder_path):          # for multi_WLs
        for filename in files:
            if re.search(file_pattern, filename):
                df_tmp = pd.read_table(os.path.join(folder_path, filename), sep=' ')
                df_tmp = df_tmp.iloc[:, 1]
                if 'dr' in filename:
                    key_wd = '_'+filename.split('dr_')[1].split('_slice_')[0]
                elif 'rdc_' in filename:
                    # key_wd = '_'+filename.split('rdc_')[1].split('_rc_')[0]
                    key_wd = '_'+filename.split('rdc_')[1].split('_slice_')[0]+'BlkRD'
                else:
                    key_wd = '_'
                block = filename.split('block_num_')[1].split('_')[0]
                # wordline = filename.split('wl_')[1].split('.')[0]
                wordline = filename.split('wl_')[1].split('_')[0]
                name = rd_title(block, old_version)
                wordline_col.append('WL'+wordline+key_wd+name)
                # wordline_col.append('Blk'+block+'_'+'WL'+wordline)
                lst.append(df_tmp)
    return wordline_col, lst

def pattern_consist(pattern_dict):
    if len(pattern_dict)==0:
        pat_info = r"slice_(\d+)_chp_die_(\d+)_block_num_(\d+)_plane_(\d+)_"
    else:
        if 'slice' in pattern_dict:
            slice_id = pattern_dict['slice']
            pat_info = r"slice_" + str(slice_id) + "_"
        else:
            pat_info = r"slice_(\d+)_"

        if 'die' in pattern_dict:
            die_id = pattern_dict['die']
            pat_info = pat_info + r"chp_die_" + str(die_id) + "_"
        else:
            pat_info = pat_info + r"chp_die_(\d+)_"

        if 'block' in pattern_dict:
            block_id = pattern_dict['block']
            pat_info = pat_info + r"block_num_" + str(block_id) + "_"
        else:
            pat_info = pat_info + r"block_num_(\d+)_"

        if 'plane' in pattern_dict:
            plane_id = pattern_dict['plane']
            pat_info = pat_info + r"plane_" + str(plane_id) + "_"
        else:
            pat_info = pat_info + r"plane_(\d+)_"

    return pat_info

def Vth(folder_path, wordline_list, pattern_dict):
    
    pat_info = pattern_consist(pattern_dict)
    global lst, wordline_col
    lst, wordline_col = [], []
    if 'All' in wordline_list:
        file_pattern = pat_info + r"wl_(\d+)"
        wordline_col, lst = find_pattern(folder_path, file_pattern, True) #hang, add old version = True

    else:
        for wordline in wordline_list:
            file_pattern = pat_info + r"wl_" + str(wordline) + "_"
            print('file_pattern:',file_pattern)
            wordline_col, lst = find_pattern(folder_path, file_pattern, True) #hang, add old version = True

    df = pd.DataFrame(lst).T
    df.columns = wordline_col
    df.dropna(axis=1)
    df.drop(df.index[[(len(df)-1)]], inplace=True)
    suffix_name = folder_path.split('_')[-1]
    df.to_csv(os.path.dirname(folder_path) + r"\Vt_wl" + ".csv", mode='a+')


def Vth_Call(folder_path, wordline_list, pattern_dict, old_version):
    
    pat_info = pattern_consist(pattern_dict)
    global lst, wordline_col
    lst, wordline_col = [], []
    if 'All' in wordline_list:
        file_pattern = pat_info + r"wl_(\d+)"
        wordline_col, lst = find_pattern(folder_path, file_pattern, old_version)

    else:
        for wordline in wordline_list:
            file_pattern = pat_info + r"wl_" + str(wordline) + "_"
            wordline_col, lst = find_pattern(folder_path, file_pattern, old_version)

    return wordline_col, lst
    # df = pd.DataFrame(lst).T
    # df.columns = wordline_col
    # df.dropna(axis=1)
    # df.drop(df.index[[(len(df)-1)]], inplace=True)
    # suffix_name = folder_path.split('_')[-1]
    # df.to_csv(os.path.dirname(folder_path) + r"\Vt_wl" + ".csv", mode='a+')


def page_data(name, mode, device):
    f=open(name)
    data=[]
    for line in f:
        a=line.split(' ')
        if a[0]=='Data_end\n':
            break
        if a[0]!= '\n' and a[0]!='brbypass' and a[0]!='brdrbp' and a[0]!='Data_start\n' and a[0]!='setr' and a[0]!='cdist':
            data+=a
        
    data.remove('\n')
    data=[int(a) for a in data]

    addr = dict()
    wl = name.split('_')[-3]
    string = int(wl) % 4
    layer = int(wl) // 4
    cell = []

    # LSB
    addr['A5'] = data[0:256]
    addr['A9'] = data[(0+256):(256+256)]

    # CSB
    addr['A6'] = data[(0+256*2):(256+256*2)]
    addr['A8'] = data[(0+256*3):(256+256*3)]
    addr['AA'] = data[(0+256*4):(256+256*4)]

    # MSB
    addr['A7'] = data[(0+256*5):(256+256*5)]
    addr['AB'] = data[(0+256*6):(256+256*6)]

    cell = mode

    f.close()
    return layer, wl, string, cell, addr


# *************************** Below Merge All Testing Data into One CSV *************************** #
def txt2csv_merge(txt_file, i, Test_for, df_init):

    slice = (os.path.splitext(os.path.basename(txt_file))[0].split('slice_')[1]).split('_')[0]
    die = (os.path.splitext(txt_file)[0].split('die_')[1]).split('_')[0]
    block = (os.path.splitext(txt_file)[0].split('block_num_')[1]).split('_')[0]
    plane = (os.path.splitext(txt_file)[0].split('plane_')[1]).split('_')[0]
    cycle = os.path.splitext(txt_file)[0].split('_')[-1]

# ************************ Partial Program Setting ************************
    if 'Partial+DR' in Test_for:
        if block in blk_10P:
            Part_Ratio = '10%'
        elif block in blk_25P:
            Part_Ratio = '25%'
        elif block in blk_50P:
            Part_Ratio = '50%'
        elif block in blk_75P:        
            Part_Ratio = '75%'
        elif block in blk_100P:
            Part_Ratio = '100%'
        Prog = ' Partially Prog'

    elif 'Partial+SPRD' in Test_for or 'Partial+BlkRD' in Test_for:
        Part_Ratio = Ratio_RD
        Prog = ' Partially Prog '

    else:
        Part_Ratio = ''
        Prog = 'Normal Prog '
    
    Type = Part_Ratio + Prog
    htdr = 0

    # ********************************************************************************************************
    teststep = os.path.splitext(os.path.basename(txt_file))[0].split('_slice')[0]

    # ************************************ BelowL: Retention Setting ************************************
    if 'DR' in Test_for:
        if 'sanity' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'DR @85C: 0hrs'
        if 'retention_data' in teststep:
            hour = teststep.split('hrs')[0].split('_')[-1]
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'DR @85C: ' + hour + 'hrs'
        if 'close_blk_data' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'DR @85C: 110hrs'
            Type = 'Close Partial Block'

        # read_temp = (teststep.split('@')[2]).split('_')[0]
        # pd_temp = (teststep.split('@')[1]).split('_')[0]

        stress_name = 'HTDR[hrs]'
        stress_value = teststep.split(': ')[1]
    
    # ************************************ BelowL: Read Disturb Setting ************************************
    if 'SPRD' in Test_for:
        if 'sanity' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'SPRD @25C: 0M'
        if 'before_sprd_data' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'SPRD @25C: 0M'
        if 'close_blk_data' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'SPRD @25C: 0M'
            Type = 'Close Partial Block'    
        if 'rdc' in teststep:        
            rd = teststep.split('rdc_')[1].split('_')[0]
            Type = Part_Ratio + Prog + '& SPRD'
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'SPRD @25C: ' + str(int(float(rd)/1000000)) + 'M'

        # read_temp = (teststep.split('@')[2]).split('_')[0]
        # pd_temp = (teststep.split('@')[1]).split('_')[0]
        stress_name = 'SPRD[M]'
        stress_value = teststep.split(': ')[1]

    # ************************************ BelowL: Read Disturb Setting ************************************
    if 'BlkRD' in Test_for:
        if 'sanity' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'BlkRD @25C: 0K'
        if 'before_blkrd_data' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'BlkRD @25C: 0K'
        if 'close_blk_data' in teststep:
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'BlkRD @25C: 0K'
            Type = 'Close Partial Block'
            # Wordline = 179
        if 'rdc' in teststep:           
            rd = teststep.split('rdc_')[1].split('_')[0]
            Type = Part_Ratio + Prog + '& BlkRD'
            teststep = 'E/P @25C_' + ' @25C' + '_' + 'BlkRD @25C: ' + str(int(float(rd)/1000)) + 'K'

        # read_temp = (teststep.split('@')[2]).split('_')[0]
        # pd_temp = (teststep.split('@')[1]).split('_')[0]
        stress_name = 'BlkRD[K]'
        stress_value = teststep.split(': ')[1]

    layer, wl, string, cell, addr = page_data(txt_file, mode, device)
    df = pd.DataFrame()
    df['DAC'] = np.append(np.linspace(128, 255, 128), np.linspace(0, 127, 128))
    df['mV'] = np.append(np.linspace(-1280, -10, 128), np.linspace(0, 1270, 128))

    for key, value in addr.items():
        x= np.array(value)
        df[key] = value
        if key == 'A8' or key == 'A9' or key == 'AB':
            df[key+'_Diff'] = np.append(0, np.diff(x)) * -1
        else:
            df[key+'_Diff'] = np.append(0, np.diff(x))
        # df['Addr'] = [key] * len(value)

    df_init = pd.concat([df_init, df], ignore_index=True)
    df_init = df_init.reindex(columns=['DAC', 'mV', 'A5', 'A6', 'A7', 'A8', 'A9', 'AA', 'AB', 'A5_Diff', 'A6_Diff', 'A7_Diff', 'A8_Diff', 'A9_Diff', 'AA_Diff', 'AB_Diff'])
  
    df_init['Cell'] = cell  
    df_init['Cycle'] = cycle
    df_init['Block'] = block
    df_init['Plane'] = plane
    df_init['Layer'] = layer
    df_init['WordLine'] = wl
    # df_init['String'] = string
    df_init['Slice_Die'] = str(slice) + '_' + str(die)
    df_init['TestStep'] = teststep
    df_init['Type'] = Type
    # df_init['Read_temp'] = read_temp
    # df_init['PD_temp'] = pd_temp
    # df_init['PD_temp_Read_temp'] = pd_temp + '_' + read_temp
    df_init[stress_name] = htdr

    if i == 0:
        df_init.to_csv(folder_path + r'\Vt_initial_data.csv', index=False, header=1, mode='a+')
    else:
        df_init.to_csv(folder_path + r'\Vt_initial_data.csv', index=False, header=0, mode='a+')

# *************************** End: Vt *************************** #


if __name__ == '__main__':

    list_item = None
    print('Test Info: Device:{0} - Mode:{1} - Test_for:{2} - Path:{3}'.format(device, mode, Test_for, os.path.dirname(path)))

    if 'WDC' in device or 'BiCS' in device or 'YMTC' in device or 'X29060' in device:
        wordline = 0
        folder_list = []
        pattern_dict = {'block': 15}
        for folder in os.listdir(path):
            if 'vt_for' in folder:
                df_Vt = pd.DataFrame()
                folder_path = os.path.join(path, folder)
                print('Process for folder:', '"' + folder + '"') 
                Vth(folder_path, wordline_list=['All'], pattern_dict=pattern_dict)       

### ***************************** Setting for Partial Prog Condition ***************************** ###
    if 'Micron' in device or 'B47R' in device:
        if 'Partial+DR' in Test_for:
            blk_10P = [360,369,400] # 10%
            blk_25P = [44,48,52,56] # 25%
            blk_50P = [84,88,92,96] # 50% should be 351
            blk_75P = [124,128,132,136] # 75%
            blk_100P = [164,168,172,176] # 100%

        if 'Partial+SPRD' in Test_for or 'Partial+BlkRD' in Test_for:
            Ratio_RD = '25%'

        folder_list = []
        for folder in os.listdir(path):
            if 'vt_for' in folder:
                df_Vt = pd.DataFrame()
                folder_path = os.path.join(path, folder)
                print('Process for folder:', '"' + folder + '"') 

                for csv in os.listdir(folder_path):
                    if os.path.join(folder_path,csv).endswith('.csv') and csv !='log.csv':
                        file_path = os.path.join(folder_path, csv)
                        os.remove(file_path)

                txt_file_list = [os.path.join(folder_path, file) for file in os.listdir(folder_path) if os.path.join(folder_path, file).endswith('.txt')]
                i = 0  
                for txt_file in txt_file_list:
                    txt2csv_merge(txt_file, i, Test_for, df_Vt)
                    i = i+1

        vt_merge(path)

