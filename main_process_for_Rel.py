import os
import gc
import time
import traceback
import threading
import pandas as pd
from tqdm import tqdm
import multiprocessing
from pickle import NONE
from threading import RLock
from pyfiglet import Figlet
from datetime import datetime
from fractions import Fraction
from multiprocessing.dummy import Pool as ThreadPool
from multiprocessing import Lock, Manager

import nandinfo as ndinfo
from plat_form.nplust import *
from function.hit_rate import *
from function.process_data import *
from device.device_configure import *
from colorama import init, Fore, Back, Style
import csv
init(autoreset=True)

def execute_func(folder, pids_list, iteration, ratio, process_lock, print_dict, result_queue):
    try:
        generate_initial_file_flag = True
        generate_optimum_file_flag = True
# ********************************************************************************* #

        df_init_csv = pd.DataFrame()
        df_blk_csv = pd.DataFrame()
        df_optimum_by_page = pd.DataFrame()

        time.sleep(2)
        with process_lock: 
            if print_dict['print']==True and (only_merge==0) and (fbc_process_flag==1):
                print_dict['print'] = False
                pids_list.append(str(os.getpid()))
                print(Fore.LIGHTMAGENTA_EX + '\nExecuting {}th - Progressing: {:.2%}'.format(iteration, ratio))
                print(Fore.BLUE + '   [' + str(datetime.now()) + '] -> Processing TxT/Init Files')

        folder_path = os.path.join(ndinfo.path,'RawData', folder) #单个raw data folder
        store_dir = os.path.join(ndinfo.path, 'Outcome', folder)    #单个处理数据保存的folder

        if not os.path.exists(store_dir):
            os.makedirs(store_dir)

        if del_csv_flag: # delete merged csv-files under 'Outcome'
            with process_lock:
                del_csv(os.path.join(ndinfo.path, 'Outcome'))

        suffix_name = folder_path.split('_')[-1] #stress in folder name, e.g. 2K
        if (fbc_process_flag and (only_merge==0)):               
            if generate_initial_file_flag:
                if 'Drive' in platform:
                    df_init_csv = txt2csv_file(folder_path, store_dir, suffix_name, pids_list) #, ratio
                elif 'NplusT' in platform:
                    df_init_csv = init_nplust(folder_path, pids_list, store_dir, suffix_name)
                # block_level_process(store_dir, df_blk_csv, suffix_name, pids_list)

            df_optimum_by_page = page_level_process(store_dir, df_init_csv, generate_optimum_file_flag, suffix_name, pids_list) # , ratio ##每个wordline每个Level找出FBC最小的RR case和 default read.
            
        if filter_RR_flag: 
            filter_rr(store_dir, suffix_name, folder) #hang, output filter rr file for each folder and merge MaxFBC by Cycle_Type_Level_RR_WLType

        # hit_rate_enable = 0 #hang, add to skip hit_rate process
        if (only_merge==0) and hit_rate_enable: 
            hit_rate(df_optimum_by_page, df_init_csv, store_dir, suffix_name)
        else:  
            del df_optimum_by_page
            del df_init_csv
            gc.collect()
        
        print_dict['print']=True
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def merge_func(msg_info, keyword='',path = '', folder_num = 1):
    try:
        if msg_info:
        # if False:
            #merge all csv file without data process
            with tqdm(range(len(msg_info)), desc=Fore.GREEN + '     [Merging Csv Files for {}th Folder'.format(folder_num) + ']: ', colour='GREEN', ncols=120, position=0) as tbar:
                for key, csv_path in msg_info:
                    key, cur_path = key, path
                    if path == '':
                        cur_path = os.path.dirname(os.path.dirname(os.path.dirname(csv_path))) #hang, Outcome上级文件夹
                    else:
                        cur_path = path

                    if only_merge:
                        key = r'/merged_' + keyword  
                    else:
                        key = r'/comb_' + key

                    def custom_reader(csvfile):
                            with open(csv_path, 'r', newline='') as csvfile:
                                csvreader = csv.reader(csvfile)
                                for row in csvreader:
                                    yield row

                    if 'HitRate' in key:  # 'HitRate_Ratio' in key or 'HitRate_Optimum' in key
                        df = pd.DataFrame(custom_reader(open(csv_path,'r'))) #这里的df是一个generator转换成的dataframe，原来csv里面的第一行不是列名，而是数据
                        new_index = pd.RangeIndex(start=0, stop=34)
                        df = df.reindex(columns=new_index)
                        df.columns = df.iloc[0] #将第一行作为列名
                        df = df.drop(0) #删除第一行，也就是原来列名行
                        df['TestTemp'] = os.path.dirname(csv_path).split('_')[-3]
                        col = df.pop('TestTemp') # remove column and return as series
                        df.insert(loc=0, column='TestTemp', value = col)   # insert column in dataframe
                    else:    
                        df = pd.read_csv(csv_path, dtype={"Max_BER": str, "Avg_BER": str, "BER": str, "SFR": str}, low_memory=False)

                    if os.path.exists(cur_path + key + '.csv'):
                        df.to_csv(cur_path + key + '.csv', index=False, header=False, mode='a+')
                    else:
                        df.to_csv(cur_path + key + '.csv', index=False, header=True, mode='w+')
                    tbar.update()
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def HitRateProcess(keyword=''):
    try:
        if 'HitRate' in keyword:    #re-structure merged hitrate_ratio csv files
            if only_merge:
                file_start_with = r'/merged_' #/Outcome
            else:
                file_start_with = r'/comb_' #/Outcome

            combinedf = pd.read_csv(ndinfo.path + file_start_with + keyword + '.csv', header=None, low_memory=False)
            if 'by_cycle_type_stress_level_WL_RRGroup' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs']
                newdf_colcount = 12
                readtype_col = 8
                no_name_col_num = 25
            elif 'by_cycle_type_stress_WL_RRGroup_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs']
                newdf_colcount = 11
                readtype_col = 7
                no_name_col_num = 26
            elif 'by_type_stress_WL_RRGroup_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs']
                newdf_colcount = 10
                readtype_col = 6
                no_name_col_num = 27 
            elif 'by_cycle_type_stress_level_WL' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs']
                newdf_colcount = 11
                readtype_col = 7
                no_name_col_num = 26
            elif 'by_cycle_type_stress_WL_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs']
                newdf_colcount = 10
                readtype_col = 6
                no_name_col_num = 27
            elif 'by_type_stress_WL_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs']
                newdf_colcount = 9
                readtype_col = 5
                no_name_col_num = 28              
            elif 'by_cycle_stress_level_type' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs']
                newdf_colcount = 10
                readtype_col = 6
                no_name_col_num = 27
            elif 'by_cycle_type_stress_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs']
                newdf_colcount = 9
                readtype_col = 5
                no_name_col_num = 28
            elif 'by_type_stress_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC']
                RRdf_columnlist = ['TestTemp','Type','DR','Total_CWs']
                newdf_colcount = 8
                readtype_col = 4
                no_name_col_num = 29
            
            combinedf.columns = conbinedf_columnlist + list(range(1,no_name_col_num))
            combinedf = combinedf.drop(0)
            combinedf.insert(loc=readtype_col, column='ReadType', value = 'DefaultRead')   # insert column 'ReadType' in dataframe
            # print(combinedf.head(10))
            newdf = pd.DataFrame(columns = newdf_columnlist)

            for row in range(1, combinedf.shape[0]+1):
                rawdata = combinedf.loc[row].dropna() #rawdata 是一行数据，list的形式，使用dropna后，rawdata长度发生变化
                rawdatadf = rawdata.to_frame().T #转换成dataframe,同时行名变成列名
                
                if len(rawdata) > newdf_colcount:
                    Defaultdf = rawdatadf.loc[:,newdf_columnlist[0:-1]] #取出前面的列
                    # print(Defaultdf.head(10))
                    newdf = pd.concat([newdf, Defaultdf], ignore_index=True)
                    # print(len(rawdata))
                    RRcount = (len(rawdata) - newdf_colcount) // 3
                    for i in range(0, RRcount):
                        if i == RRcount-1 and (len(rawdata) - newdf_colcount) % 3 != 0:
                            RRdf = rawdatadf.loc[:,RRdf_columnlist]
                            RRdf['ReadType'] = rawdatadf.loc[:,1+i*3]
                            RRdf['Passed_CWs'] = rawdatadf.loc[:,2+i*3]
                            RRdf['Pass_Rate'] = rawdatadf.loc[:,3+i*3]
                            RRdf['MaxFBC'] = rawdatadf.loc[:,4+i*3]
                        else:
                            RRdf = rawdatadf.loc[:,RRdf_columnlist]
                            RRdf['ReadType'] = rawdatadf.loc[:,1+i*3]
                            RRdf['Passed_CWs'] = rawdatadf.loc[:,2+i*3]
                            RRdf['Pass_Rate'] = rawdatadf.loc[:,3+i*3]
                        newdf = pd.concat([newdf, RRdf], ignore_index=True)
                else:
                    newdf = pd.concat([newdf, rawdatadf], ignore_index=True)

            if os.path.exists(ndinfo.path + file_start_with + 'New_' + keyword + '.csv'):
                newdf.to_csv(ndinfo.path + file_start_with + 'New_' + keyword + '.csv', index=False, header=False, mode='a+')
            else:
                newdf.to_csv(ndinfo.path + file_start_with + 'New_' + keyword + '.csv', index=False, header=True, mode='w+')                                  
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

def SelectedHitRateProcess(keyword=''):
    try:
        if 'HitRate' in keyword:    #re-structure merged hitrate_ratio csv files
            if only_merge:
                file_start_with = r'/merged_' #/Outcome
            else:
                file_start_with = r'/comb_' #/Outcome

            combinedf = pd.read_csv(ndinfo.path + file_start_with + keyword + '.csv', header=None, low_memory=False)
            if 'by_cycle_type_stress_level_WL_RRGroup' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','RRGroup','Total_CWs'] #这个相当于固定的列                
                no_name_col_num = 24
            elif 'by_cycle_type_stress_WL_RRGroup_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','RRGroup','Total_CWs']
                no_name_col_num = 25
            elif 'by_type_stress_WL_RRGroup_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Type','DR','WLType','RRGroup','Total_CWs']
                no_name_col_num = 26             
            elif 'by_cycle_type_stress_level_WL' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','WLType','Total_CWs'] #这个相当于固定的列                
                no_name_col_num = 25    #columns no name in combinedf                
            elif 'by_cycle_type_stress_WL_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','WLType','Total_CWs']
                no_name_col_num = 26
            elif 'by_type_stress_WL_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Type','DR','WLType','Total_CWs']
                no_name_col_num = 27               
            elif 'by_cycle_stress_level_type' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','Level','DR','Total_CWs']
                no_name_col_num = 26
            elif 'by_cycle_type_stress_no_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Cycle','Type','DR','Total_CWs']
                no_name_col_num = 27
            elif 'by_type_stress_no_cycle_level' in keyword:
                conbinedf_columnlist = ['TestTemp','Type','DR','Total_CWs','Passed_CWs','Pass_Rate','Failed_CWs']
                newdf_columnlist = ['TestTemp','Type','DR','Total_CWs','ReadType','Passed_CWs','Pass_Rate','Failed_CWs','MaxFBC','RR_Order','Failed_CWs_Pass_Rate']
                RRdf_columnlist = ['TestTemp','Type','DR','Total_CWs']
                no_name_col_num = 28

            newdf_colcount = len(conbinedf_columnlist) + 1      #conbinedf 插入'readtype'后的列数
            readtype_col = len(conbinedf_columnlist) -3         #'readtype' 插入到 conbinedf 的第几列， 倒数第四列
            combinedf = combinedf.drop(combinedf.columns[[len(conbinedf_columnlist)+4]],axis=1)     #删除'Joint1stRR_Failed_CW: 0'在的那一列

            combinedf.columns = conbinedf_columnlist + list(range(1,no_name_col_num))   #rename columns, 没有列名的那些col从1开始命名
            combinedf = combinedf.drop(0)   #删除第一行？
            combinedf.insert(loc=readtype_col, column='ReadType', value = 'DefaultRead')   # insert column 'ReadType' in dataframe
            
            newdf = pd.DataFrame(columns = newdf_columnlist)
            RRindex =1
            pre_key = ''
            for row in range(1, combinedf.shape[0]+1):
                rawdata = combinedf.loc[row].dropna() #rawdata 是一行数据，list的形式，使用dropna后，rawdata长度发生变化
                rawdatadf = rawdata.to_frame().T #转换成dataframe,同时行名变成列名
                combined_key = rawdatadf.iloc[:, 0:len(RRdf_columnlist)-1].apply(lambda x: '_'.join(map(str, x)), axis=1) #将前面的列合并得到combined_key
                key = combined_key.iloc[0]  #取出combined_key的第一个值
                total_failed_CWs = rawdatadf.loc[:,'Failed_CWs'].astype(int).iloc[0] #取出第一个值
                if key == pre_key:
                    RRindex += 1
                else:
                    RRindex = 1

                if len(rawdata) > newdf_colcount:
                    Defaultdf = rawdatadf.loc[:,newdf_columnlist[0:-3]] #取出前面的列, 只包含default read数据
                    newdf = pd.concat([newdf, Defaultdf], ignore_index=True)    #将default read数据放到newdf里
                    RRcount = (len(rawdata) - newdf_colcount) // 3
                    RR_decode_CWs = 0
                    for i in range(0, RRcount):
                        # if i == RRcount-1 and (len(rawdata) - newdf_colcount) % 3 != 0:
                        if i == 0:
                            RRdf = rawdatadf.loc[:,RRdf_columnlist]
                            RRdf['ReadType'] = rawdatadf.loc[:,1+i*3]
                            RRdf['Passed_CWs'] = rawdatadf.loc[:,2+i*3]
                            RRdf['Pass_Rate'] = rawdatadf.loc[:,3+i*3]
                            RRdf['MaxFBC'] = rawdatadf.loc[:,4+i*3]
                            RRdf['Failed_CWs'] = total_failed_CWs - RRdf['Passed_CWs'].astype(int)
                            RRdf['RR_Order'] = RRindex
                            RRdf['Failed_CWs_Pass_Rate'] = '{:0.2%}'.format(float(RRdf.loc[:,'Passed_CWs'].astype(int) / total_failed_CWs))
                        else:
                            RRdf = rawdatadf.loc[:,RRdf_columnlist]
                            RRdf['ReadType'] = rawdatadf.loc[:,1+(i-1)*3+4]
                            RRdf['Passed_CWs'] = rawdatadf.loc[:,2+(i-1)*3+4]
                            RRdf['Pass_Rate'] = rawdatadf.loc[:,3+(i-1)*3+4]
                            RRdf['Failed_CWs'] = remained_failed_CWs - RRdf['Passed_CWs'].astype(int)
                            RRdf['RR_Order'] = RRindex
                            RRdf['Failed_CWs_Pass_Rate'] = '{:0.2%}'.format(float(RRdf.loc[:,'Passed_CWs'].astype(int) / total_failed_CWs))
                        newdf = pd.concat([newdf, RRdf], ignore_index=True)
                        remained_failed_CWs = total_failed_CWs - RRdf['Passed_CWs'].astype(int)      #计算剩余的failed_CWs
                else:
                    newdf = pd.concat([newdf, rawdatadf], ignore_index=True)
                pre_key = key

            if os.path.exists(ndinfo.path + file_start_with + 'New_' + keyword + '.csv'):
                newdf.to_csv(ndinfo.path + file_start_with + 'New_' + keyword + '.csv', index=False, header=False, mode='a+')
            else:
                newdf.to_csv(ndinfo.path + file_start_with + 'New_' + keyword + '.csv', index=False, header=True, mode='w+')                                  
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())

# def get_pids(pools):
#     pid_list = []
#     for process in range(len(pools)):
#         pid = str(pools[process]).split('pid=')[1].split(' ')[0]
#         pid_list.append(pid)
#     return pid_list

## 在 Windows 和 Linux 上，multiprocessing.Pool 处理进程对象的字符串格式可能会有所不同。
## 因此，当你在 Linux 上运行时，split('pid=') 可能无法正常工作，导致 IndexError: list index out of range 错误。
## 为了让代码在不同操作系统上都能正常运行，你可以使用 Python 的标准库来直接获取进程 ID，而不是依赖字符串的格式。
## multiprocessing.Pool 中的每个进程对象有一个 .pid 属性，它可以安全地获取进程 ID。
## modify by AI
def get_pids(pools):
    pid_list = [str(process.pid) for process in pools]
    return pid_list


def main():
    try:
        f = Figlet(font='rounded', width=6000)
        print(f.renderText('DATA PROCESS'))

        if only_merge:
            print(Fore.BLUE + '[' + str(datetime.now()) + ']  Only Merge Files')

            # file_keyword_list = ['Optimum_Read_by_page',
            #                         'HitRate_Ratio_by_cycle_type_stress_level_WL','HitRate_Ratio_by_cycle_type_stress_WL_no_level','HitRate_Ratio_by_type_stress_WL_no_cycle_level',
            #                         'HitRate_Ratio_by_cycle_stress_level_type','HitRate_Ratio_by_cycle_type_stress_no_level','HitRate_Ratio_by_type_stress_no_cycle_level',
            #                         'HitRate_Optimum_by_cycle_type_stress_level_WL','HitRate_Optimum_by_cycle_type_stress_WL_no_level','HitRate_Optimum_by_type_stress_WL_no_cycle_level',
            #                         'HitRate_Optimum_by_cycle_stress_level_type','HitRate_Optimum_by_cycle_type_stress_no_level','HitRate_Optimum_by_type_stress_no_cycle_level',
            #                         'HitRate_SelectedRR_by_cycle_type_stress_level_WL','HitRate_SelectedRR_by_cycle_type_stress_WL_no_level','HitRate_SelectedRR_by_type_stress_WL_no_cycle_level',
            #                         'HitRate_SelectedRR_by_cycle_stress_level_type','HitRate_SelectedRR_by_cycle_type_stress_no_level','HitRate_SelectedRR_by_type_stress_no_cycle_level']
            # file_keyword_list = ['HitRate_SelectedRR_by_cycle_type_stress_level_WL','HitRate_SelectedRR_by_cycle_type_stress_WL_no_level','HitRate_SelectedRR_by_type_stress_WL_no_cycle_level',
            #                         'HitRate_SelectedRR_by_cycle_stress_level_type','HitRate_SelectedRR_by_cycle_type_stress_no_level','HitRate_SelectedRR_by_type_stress_no_cycle_level']
            file_keyword_list = ['Optimum_Read_by_page']
            for file_keyword in file_keyword_list:
                file_list = []
                for folder in os.listdir(os.path.join(path, 'Outcome')): #遍历上面产生的文件夹
                    if folder_keyword in folder: 
                        for file in os.listdir(os.path.join(path, 'Outcome',folder)):
                            if (file_keyword + '_' + folder.split('_')[-1]) in  file:
                                file_list.append(os.path.join(path, 'Outcome',folder,file))       
                merge_func(list(enumerate(file_list)),keyword = file_keyword)
                if 'SelectedRR' in file_keyword:
                    SelectedHitRateProcess(keyword = file_keyword) #hang
                else:
                    HitRateProcess(keyword = file_keyword) #hang

                

        else:
            folder_list = find_folder(path) #hang, 添加了RawData folder
            # folder_list = find_outcome_folder(path) #hang, 添加了Outcome folder
            # folder_list = find_folder(path+'/Outcome') #hang, this for merge filter RR data
            # folder_list = ['data_for_25P_SPRD_CloseAllWLs','data_for_30P_SPRD_CloseAllWLs','data_for_35P_SPRD_CloseAllWLs'] #hang

            pool = multiprocessing.Pool(pool_nums) # multiprocessing.cpu_count()
            manager = Manager()
            result_queue, process_lock = manager.Queue(), manager.Lock()
            print_dict = manager.dict()
            print_dict['print'] = True
            pids_list = manager.list()

            def poolError(e):
                print(Fore.RED + '{}'.format(e),"Error")

            pool_cnt, file_cnt, iteration = len(get_pids(pool._pool)), len(folder_list), 1
            for N in range(len(folder_list)): #已按照segmentname切分的raw data的文件夹
                if pool_cnt*iteration <= file_cnt:
                    ratio = (pool_cnt*iteration)/(file_cnt)
                else:
                    ratio = 1
                pool.apply_async(func=execute_func, args=(folder_list[N], pids_list, iteration, ratio, process_lock, print_dict, result_queue), error_callback=poolError) # args=(folder_list[N])
                if (N + 1) % pool_cnt == 0:
                    iteration += 1 
            pool.close()
            pool.join()

            if merge_folder_flag:
                print(Fore.MAGENTA + '\n[' + str(datetime.now()) + '] -> Merge Files now ...')
                folder_num = 1
                for folder in folder_list:
                    store_dir = os.path.join(ndinfo.path, 'Outcome', folder)
                    merge_dict = file_merge(store_dir, merge_file)
                    merge_func(merge_dict.items(), folder_num=folder_num)
                    folder_num += 1

                # file_keyword_list = ['HitRate_Ratio_by_cycle_type_stress_level_WL','HitRate_Ratio_by_cycle_type_stress_WL_no_level','HitRate_Ratio_by_type_stress_WL_no_cycle_level',
                #                      'HitRate_Ratio_by_cycle_stress_level_type','HitRate_Ratio_by_cycle_type_stress_no_level','HitRate_Ratio_by_type_stress_no_cycle_level',
                #                      'HitRate_Optimum_by_cycle_type_stress_level_WL','HitRate_Optimum_by_cycle_type_stress_WL_no_level','HitRate_Optimum_by_type_stress_WL_no_cycle_level',
                #                      'HitRate_Optimum_by_cycle_stress_level_type','HitRate_Optimum_by_cycle_type_stress_no_level','HitRate_Optimum_by_type_stress_no_cycle_level',
                #                      'HitRate_SelectedRR_by_cycle_type_stress_level_WL','HitRate_SelectedRR_by_cycle_type_stress_WL_no_level','HitRate_SelectedRR_by_type_stress_WL_no_cycle_level',
                #                      'HitRate_SelectedRR_by_cycle_stress_level_type','HitRate_SelectedRR_by_cycle_type_stress_no_level','HitRate_SelectedRR_by_type_stress_no_cycle_level']
                # file_keyword_list = ['HitRate_SelectedRR_by_cycle_type_stress_level_WL','HitRate_SelectedRR_by_cycle_type_stress_WL_no_level','HitRate_SelectedRR_by_type_stress_WL_no_cycle_level',
                #                     'HitRate_SelectedRR_by_cycle_stress_level_type','HitRate_SelectedRR_by_cycle_type_stress_no_level','HitRate_SelectedRR_by_type_stress_no_cycle_level']
                if split_WL_zone_flag == 1:
                    file_keyword_list = ['HitRate_Ratio_by_cycle_type_stress_level_WL_RRGroup','HitRate_Ratio_by_cycle_type_stress_WL_RRGroup_no_level','HitRate_Ratio_by_type_stress_WL_RRGroup_no_cycle_level']
                else:
                    file_keyword_list = ['HitRate_Ratio_by_cycle_type_stress_level_WL','HitRate_Ratio_by_cycle_type_stress_WL_no_level','HitRate_Ratio_by_type_stress_WL_no_cycle_level',
                                        'HitRate_Ratio_by_cycle_stress_level_type','HitRate_Ratio_by_cycle_type_stress_no_level','HitRate_Ratio_by_type_stress_no_cycle_level']             
                for file_keyword in file_keyword_list: #hang
                    if 'SelectedRR' in file_keyword:
                        SelectedHitRateProcess(keyword = file_keyword) #hang
                    else:
                        HitRateProcess(keyword = file_keyword) #hang
        
    except Exception as ex:
        print(Fore.RED + 'Error: ', traceback.format_exc())
    
'''
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Script <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<
'''
if __name__ == '__main__':

    start_time = time.time()

    print('\nTest Info >>> Device: {0} - Plane: {1} - Mode: {2} - test_for: {3} - Dir Path: {4}'.format(device, plane, mode, test_for, os.path.dirname(path)))
    
    main()
        
    end_time = time.time()

    print()
    print(Fore.MAGENTA + 'Running time: {0:.2f} Seconds/{1:.2f} Minutes\n\n'.format((end_time - start_time), (end_time - start_time)/60))


