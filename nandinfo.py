import os
from variable import globalvar as glb
glb._init()

'''
DataProcess_for_Drive_Rel.py:
    1. Merge file: Once file finished processing, merge this file immetiately according to 'merge_folder_flag' and merge_file: Line 23;
    2. Skip some folders not to process: If the folders have been processed then skip these folders ->
        / skip those folders -> if file in those folders containing 'skip_keyword': Line 56;
'''

path = r'/nandfile/hang/YMTC_X36070/2_BLKRD/Test_with_RRTable/'

list_item = [] # None # (list_item = None or = ['item]) #['Ea']  #'TestTemp', 'Type','SPRD_Flag'
# add_col = ['Ea=1.0V']
platform = glb.set_value('platform', 'NplusT')
device = glb.set_value('device', 'X36070')
folder_keyword = 'data_for_' # search wanted folders via 'folder_keyword'
test_for = 'BlkRD' # Partial+BlkRD; Partial+SPRD; Partial+DR; SPRD; BlkRD; DR: including HTDR or STDR(short term) or LTDR(long term) or RTDR(room temperture)
fbc_process_flag = 1
best_hit_rate_flag, criteria_hit_rate_flag, optimum_hit_rate_flag, maxratio_hit_rate_flag, selectRR_hit_rate_flag = 0, 0, 0, 0, 0
hit_rate_enable = best_hit_rate_flag | criteria_hit_rate_flag | optimum_hit_rate_flag | maxratio_hit_rate_flag | selectRR_hit_rate_flag
read_retry_block_count = 4 # block type fot retry (0: 2 even blocks, 1: 2 odd blocks, 4: all blocks)
merge_folder_flag, only_merge = 1, 0 # only_merge means merge specified files in any path
SPRD_Aggressor_WL = 1072 # wordline number of aggressor side for close block SPRD
pool_nums = 1 # multi_processing pool numbers
merge_file = ['Optimum_Read_by_page'] #'Initialization_data', 'Filter_RR_Read_by_page', 'Optimum_Read_by_page'
del_csv_flag = 0 #merge_folder_flag # before processing, to delete all CSV files in every store_dir's folder
filter_RR_flag = 0 # 1: filter RR; 0: not filter RR
RR_output_list = range(116) #or input the list: [0,1,2,3,4]
bad_block_list = ["1_0_0&47"] #CH_CE_LUN&Block
split_WL_zone_flag = 0 #RR offset is based on wl zone
optimum_read_by_page_specific_RR = 1 #hang, 2025.03.14, need to modify specific RR in process_data.py

skip_folder_flag = 0 # if one folder has 'skip_keyword' file, skip to process it
if skip_folder_flag:
    check_path = os.path.join(os.path.dirname(path), 'Outcome')
    # check_path = os.path.join(path, 'Outcome')
    skip_keyword = 'HitRate_Ratio_by_type_stress_WL_RRGroup_no_cycle_level' # once find out the file with this keyword inside one folder, skip the folder(not process it)

from device.device_configure import *
from device import device_configure as dev

if 'NplusT' in platform:  
    block = dev.test_block
    num_blk_per_pec = dev.test_num_blk_per_pec
    die_locator = 'CHP_CE_LUN'
else:
    die_locator = 'Slice_Die'

if 'Sanity' in test_for:
    test_stress = 'Sanity'
elif 'DR' in test_for:
    test_stress = test_for.split('+')[-1]
elif 'SPRD' in test_for:
    count_unit = 'M'
    rate = 1000000
    if count_unit != '1' or count_unit != 1:
        test_stress = 'SPRD' + '[' + count_unit + ']'
    elif count_unit == '1' or count_unit == 1:
        rate = 1
        test_stress = 'SPRD'  
elif 'BlkRD' in test_for:
    count_unit = 'K'
    rate = 1000
    if count_unit == 'K':
        test_stress = 'BlkRD' + '[' + count_unit + ']'
    elif count_unit == '1' or count_unit == 1:
        rate = 1
        test_stress = 'BlkRD'
else:
    print("Not Supported test item???")
    sys.exit(1)

if 'SPRD' in test_for and 'DR' in test_for:
    count_unit = 'M'
    rate = 1000000
    test_stress = 'SPRD+DR'

suf_name_200bits = 'by_cycle_stress_level_type' #'by_cycle_level_stress_no_type'
if criteria_hit_rate_flag: #HitRate_200bit 里面会加上list_item
    group_list_for_200bits = ['Type', 'Cycle', 'Level', test_stress] # If excluding item 'Level', sometimes we couldn't find RR entry to decode all LSB/CSB/MSB successfully, like partially program retention test;

suf_name_decode = 'by_cycle_stress_level_type'  #'by_cycle_type_stress_no_level' 
if optimum_hit_rate_flag or maxratio_hit_rate_flag or selectRR_hit_rate_flag: #这个三个HitRate处理不会加上list_item
    group_list_for_CWs = ['Cycle', 'Type', 'Level', test_stress] #add 'Level'

if 'Partial' in test_for:
    blk_freq = 380   #hang, 40-> 80
    test_num_blk_per_pec = 5
    base_blk = {'0K PEC': [20], '1K PEC': [28], '2K PEC': [36], '3K PEC': [44], '4K PEC': [52], '5K PEC': [64]} 
    #'0K PEC': [20], '3K PEC': [32], '7K PEC': [40], '10K PEC': [48]
    # '0K PEC': [16], '3K PEC': [20], '7K PEC': [32], '10K PEC': [40]
    partial_prog_ratio = { '0_0_0':  ['10%','30%','50%','60%'],
                            '1_0_0':  ['80%','97%','99%','100%'], 
                        #   'All':  ['10%', '42%', '52%', '68%', '86%'] 
                        # '0_1_0':  ['10%', '25%', '30%', '50%', '75%'], '1_1_0':  ['80%', '90%', '96%', '99%', '100%'],
                        #   'All':  ['2%', '25%', '50%', '75%', '98%'], 
                        #   'All':  ['10%', '25%', '46%', '65%', '89%'],                          
                            # '0_1_0':  ['10%', '25%', '50%', '75%', '100%'],
                            # 'All':  ['3%', '27%', '50%', '53%', '89%'], #'90%',
                            # 'Die0':  ['10%', '25%', '50%', '75%', '90%', '100%'],
                         }
    partial_prog_wl =  {'10%': 141, '30%': 424, '50%': 704, '60%': 847, '80%': 1132, '97%': 1376, '99%': 1394, '100%': 1415} 
    # '10%': 195, '42%': 678, '52%': 837, '68%': 1098, '86%': 1386
    # '10%': 158, '25%': 396, '30%': 483, '50%': 787, '75%': 1195, '80%': 1278, '90%': 1444, '96%': 1535, '99%': 1591, '100%': 1599
    # '2%': 10, '25%': 113, '50%': 226, '75%': 335, '98%': 442  #B5 PP SPRD
    # '10%': 44, '25%': 113, '50%': 217, '75%': 338, '100%': 447 #B5 HTDR
    # '10%': 93, '25%': 213, '50%': 408, '75%': 618, '89%': 723 
    # {'10%': 80, '25%': 202, '50%': 404, '75%': 607, '100%': 809}  #B6
    # {'10%': 118, '25%': 282, '46%': 513, '65%': 723, '89%': 988}  #B8 SPRD new
    # {'10%': 108, '25%': 271, '50%': 537, '75%': 816, '100%': 1089}  #B8 HTDR
    # {'10%': 108, '25%': 271, '46%': 503, '65%': 712, '89%': 977}  #B8 SPRD
    
    if 'Partial+SPRD' in test_for or 'Partial+BlkRD' in test_for:
        aggressor_wl = [224,304] # read disturb aggressor wordline
        partial_prog_ratio = {'All':  ['10%','25%'],}
        partial_prog_wl = {'10%': 195,'25%':395} # the last wordline be programed
        rd_count_set = [40*(10**6), 40*(10**6), 40*(10**6), 40*(10**6)]

### ***************************************************************************************************************** ###
'''
>>> Below: generally, it's unnecessary to modify.
'''
if fbc_process_flag:
# if 1:
    merge_file = merge_file + ['page_level_SFR', 'page_level_BER', 'page_level_byWL']

if best_hit_rate_flag:
    merge_file = merge_file + ['HitRate_Best' + '_' + 'by_cycle_stress_level_type'] # 'HitRate_Best'
    merge_file = merge_file + ['HitRate_Best' + '_' + 'by_cycle_type_stress_no_level'] # 'HitRate_Best'
    merge_file = merge_file + ['HitRate_Best' + '_' + 'by_type_stress_no_cycle_level'] # 'HitRate_Best'
    if 'Partial' in test_for or 'SPRD'==test_for:
        merge_file = merge_file + ['HitRate_Best' + '_' + 'by_cycle_type_stress_level_WL'] # 'HitRate_Best'
        merge_file = merge_file + ['HitRate_Best' + '_' + 'by_cycle_type_stress_WL_no_level'] # 'HitRate_Best'
        merge_file = merge_file + ['HitRate_Best' + '_' + 'by_type_stress_WL_no_cycle_level'] # 'HitRate_Best'
 
if criteria_hit_rate_flag: #hang, add or 1 to merge file
    merge_file = merge_file + ['HitRate_200bits' + '_' + suf_name_200bits] # 'HitRate_200bits'
    if 'Partial' in test_for or 'SPRD'==test_for:
        merge_file = merge_file + ['HitRate_200bits' + '_' + 'by_cycle_type_stress_level_WL'] # 'HitRate_200bits'

if optimum_hit_rate_flag:
    merge_file = merge_file + ['HitRate_Optimum' + '_' + suf_name_decode] # 'HitRate_Best', , 'HitRate_Optimum' + '_' + 'by_stress_no_type_cycle_level'
    merge_file = merge_file + ['HitRate_Optimum' + '_' + 'by_cycle_type_stress_no_level']
    merge_file = merge_file + ['HitRate_Optimum' + '_' + 'by_type_stress_no_cycle_level']
    if 'Partial' in test_for or 'SPRD'==test_for:
        merge_file = merge_file + ['HitRate_Optimum' + '_' + 'by_cycle_type_stress_level_WL']
        merge_file = merge_file + ['HitRate_Optimum' + '_' + 'by_cycle_type_stress_WL_no_level']
        merge_file = merge_file + ['HitRate_Optimum' + '_' + 'by_type_stress_WL_no_cycle_level']

if maxratio_hit_rate_flag:
# if 1:
    if split_WL_zone_flag != 1:
        merge_file = merge_file + ['HitRate_Ratio' + '_' + suf_name_decode] # 'HitRate_Best',  , 'HitRate_Ratio' + '_' + 'by_stress_no_type_cycle_level'
        merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_cycle_type_stress_no_level']
        merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_type_stress_no_cycle_level']
    if 'Partial' in test_for or 'SPRD'==test_for:
        if split_WL_zone_flag == 1:
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_cycle_type_stress_level_WL_RRGroup'] #hang
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_cycle_type_stress_WL_RRGroup_no_level']
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_type_stress_WL_RRGroup_no_cycle_level']
        else:
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_cycle_type_stress_level_WL'] #hang
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_cycle_type_stress_WL_no_level']
            merge_file = merge_file + ['HitRate_Ratio' + '_' + 'by_type_stress_WL_no_cycle_level']

if selectRR_hit_rate_flag:
    merge_file = merge_file + ['HitRate_SelectedRR' + '_' + suf_name_decode] # 'HitRate_Best', , 'HitRate_Optimum' + '_' + 'by_stress_no_type_cycle_level'
    merge_file = merge_file + ['HitRate_SelectedRR' + '_' + 'by_cycle_type_stress_no_level']
    merge_file = merge_file + ['HitRate_SelectedRR' + '_' + 'by_type_stress_no_cycle_level']
    if 'Partial' in test_for or 'SPRD'==test_for:
        merge_file = merge_file + ['HitRate_SelectedRR' + '_' + 'by_cycle_type_stress_level_WL']
        merge_file = merge_file + ['HitRate_SelectedRR' + '_' + 'by_cycle_type_stress_WL_no_level']
        merge_file = merge_file + ['HitRate_SelectedRR' + '_' + 'by_type_stress_WL_no_cycle_level']

mode = 'QLC'
Loffset = dev.Loffset
bits_per_cell = dev.bits_per_cell
wl_per_layer = dev.wl_per_layer
fbc_criterion = 250
retry_mode = 'Read Retry'
SFR_spec = 1E-7

pids_list = []