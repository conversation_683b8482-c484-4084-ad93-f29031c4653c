import os
import xlrd
import xlwt
import shutil
import pandas as pd
from datetime import datetime
from xls2xlsx import XLS2XLSX
from collections import defaultdict
from alive_progress import alive_bar
import Vt_Eric_for_Drive as Vt

base_path = r'C:\Users\<USER>\Desktop\Testings\2_NAND_Data\0_Data-Drive\2_DR no RD\4_Power off LTDR_25C\Raw_Data_20231103_2Mon_OfflineRTDR\Raw_Data'

old_version = False
special_case = False
make_subfolder = True
device = 'X29060' # B47R
mode = 'TLC' # 'WDC TLC'; 'Micron TLC'
Test_for = 'HTDR' # Partial+BlkRD; Partial+SPRD; Partial Prog;SPRD; BlkRD

ii_1st, ii_2nd, ii_3rd = 0, 0, 0
block_freq_per_set = input('block frequnece(step) is: ')
def blocklst_dict(block_set, block_freq_per_set, block_group_num_per_set):
    blklst_dict = dict()
    for blk_num in range(block_group_num_per_set):
        blklst_dict[blk_num] = []
        for blk in block_set:
            blklst_dict[blk_num].append(int(blk) + blk_num * int(block_freq_per_set))

    print('blklst_dict: ', blklst_dict)
    return blklst_dict

for folder in os.listdir(base_path):
    if make_subfolder and os.path.isdir(os.path.join(base_path, folder)) and 'for' in folder:
        path = os.path.join(base_path, folder)
        file_list = os.listdir(path)
        # '''
        txt_file_list = [file for file in file_list if file.endswith('.txt')]
        print('Process for folder:', '"' + folder + '"')
        with alive_bar(len(txt_file_list), title= '        PID-' + str(os.getpid()) + '  File Migration', force_tty=True) as bar:
            for file in txt_file_list:
                # *********************************************** FBC & Vt Data **********************************************
                if 'sanity_data' in file:
                    key_name = file.split('EPRsanity_data')[0].split('_')[-1]
                    new_file = file.replace('pec_1.txt', 'pec_' + key_name + '.txt')
                    os.rename(os.path.join(path, file), os.path.join(path, new_file))

                    # if 'Vt' not in file:
                    #     folder = os.path.join(path, 'data_for_sanity_data')
                    # else:
                    #     folder = os.path.join(path, 'Vt_for_sanity_data')

                    # if not os.path.exists(folder):
                    #     os.makedirs(folder)
                    # shutil.move(os.path.join(path, file), folder)

                if 'SPRD' in file and ('before_sprd_data' not in file and 'close' not in file):
                    rdc = int(file.split('rdc_')[1].split('_slice_')[0])
                    if 'Vt' not in file:
                        folder = os.path.join(path, 'data_for_SPRD_' + str(int(rdc/1000000)) + 'M')
                    else:
                        folder = os.path.join(path, 'Vt_for_SPRD_' + str(int(rdc/1000000)) + 'M')

                    if not os.path.exists(folder):
                        os.makedirs(folder)  
                    shutil.move(os.path.join(path, file), folder)

                if 'BlkRDWL' in file and ('before_blkrd_data' not in file and 'close' not in file):
                    rdc = int(file.split('rdc_')[1].split('_slice_')[0])
                    if 'Vt' not in file:
                        folder = os.path.join(path, 'data_for_BlkRD_' + str(int(rdc/1000)) + 'K')
                    else:
                        folder = os.path.join(path, 'Vt_for_BlkRD_' + str(int(rdc/1000)) + 'K')

                    if not os.path.exists(folder):
                        os.makedirs(folder)
                    shutil.move(os.path.join(path, file), folder)

                # if 'close' in file:
                #     if ('Vt' not in file):
                #         folder = os.path.join(path, 'data_for_close_block')
                #     else:
                #         folder = os.path.join(path, 'Vt_for_close_block')

                #     if not os.path.exists(folder):
                #         os.makedirs(folder)
                #     shutil.move(os.path.join(path, file), folder)
                
                # for dr_time in [48,72,96,110]:
                #     if 'dr' + '_' + str(dr_time) + 'hrs' in file:
                #         time = file.split('dr_')[1].split('_slice_')[0]
                #         temp = file.split('C_')[0].split('_')[-1] + 'C'
                #         if ('Vt' not in file):
                #             folder = os.path.join(path, 'data_for_' + temp + '_HTDR_' + time)
                #         else:
                #             folder = os.path.join(path, 'Vt_for_' + temp + '_HTDR_' + time)

                #         if not os.path.exists(folder):
                #             os.makedirs(folder)
                #         shutil.move(os.path.join(path, file), folder)
                
                for slice_id in [0, 1, 2, 3]:
                    if 'slice' + '_' + str(slice_id) in file:
                        die = file.split('chp_die_')[1].split('_block')[0]
                        # time = file.split('dr_')[1].split('_slice_')[0]
                        temp = file.split('C_')[0].split('_')[-1] + 'C'
                        if ('Vt' not in file):
                            # folder = os.path.join(path, 'data_for_' + 'HTDR_50hrs_slice' + str(slice_id)) + '_' + str(die)
                            folder = os.path.join(path, 'data_for_' + 'HTDR_2mon_slice' + str(slice_id)) + '_' + str(die)
                        else:
                            folder = os.path.join(path, 'Vt_for_' + 'HTDR_close_block_slice' + str(slice_id))

                        if not os.path.exists(folder):
                            os.makedirs(folder)
                        shutil.move(os.path.join(path, file), folder)

                if special_case:
                    block_set = [10, 15, 20, 25]
                    block_group_num_per_set = 3
                    if ii_1st == 0:
                        block_dict = blocklst_dict(block_set, block_freq_per_set=block_freq_per_set, block_group_num_per_set=3)
                        ii_1st = 1

                    name_list = ['wldelay_100ms_fw', 'wldelay_100ms_fw_split6wl', 'wldelay_100ms_fw_split6wl']
                    for key in block_dict.keys():
                        for block in block_dict[key]:
                            if 'block_num' + '_' + str(block) + '_' in file:
                                if 'dr_' in file:
                                    key_wd = file.split('dr_')[1].split('_slice_')[0]
                                elif 'rdc_' in file:
                                    key_wd = file.split('rdc_')[1].split('_slice_')[0]
                                else:
                                    key_wd = ''

                                name = name_list[key]
                                if ('Vt' not in file):
                                    folder = os.path.join(path, 'data_for_' + name)
                                else:
                                    folder = os.path.join(path, 'Vt_for_' + name)

                                if not os.path.exists(folder):
                                    os.makedirs(folder)
                                shutil.move(os.path.join(path, file), folder)
                    
                    if ii_2nd == 0:
                        block_set = [30, 35, 40, 45]
                        block_dict_2 = blocklst_dict(block_set, block_freq_per_set=block_freq_per_set, block_group_num_per_set=3)
                        block_list = block_dict_2[0] + block_dict_2[1]
                        ii_2nd = 1

                    name_list = ['wldelay_300ms_fw_split6wl', 'wldelay_300ms_fw_split6wl', 'old_wldelay_100ms_dynamicwl']
                    for key in block_dict_2.keys():
                        for block in block_dict_2[key]:
                            if 'block_num' + '_' + str(block) + '_' in file:
                                if 'dr_' in file:
                                    key_wd = file.split('dr_')[1].split('_slice_')[0]
                                elif 'rdc_' in file:
                                    key_wd = file.split('rdc_')[1].split('_slice_')[0]
                                else:
                                    key_wd = ''

                                name = name_list[key]
                                if ('Vt' not in file):
                                    folder = os.path.join(path, 'data_for_' + name)
                                else:
                                    folder = os.path.join(path, 'Vt_for_' + name)

                                if not os.path.exists(folder):
                                    os.makedirs(folder)
                                shutil.move(os.path.join(path, file), folder)

                    if ii_3rd == 0:
                        block_set = [50, 55, 60, 65]
                        block_dict_3 = blocklst_dict(block_set, block_freq_per_set=block_freq_per_set, block_group_num_per_set=3)
                        ii_3rd = 1

                    name_list = ['wldelay_100ms_fw_split3wl', 'wldelay_100ms_fw_split3wl', 'new_wldelay_100ms_dynamicwl']
                    for key in block_dict_3.keys():
                        for block in block_dict_3[key]:
                            if 'block_num' + '_' + str(block) + '_' in file:
                                if 'dr_' in file:
                                    key_wd = file.split('dr_')[1].split('_slice_')[0]
                                elif 'rdc_' in file:
                                    key_wd = file.split('rdc_')[1].split('_slice_')[0]
                                else:
                                    key_wd = ''

                                name = name_list[key]
                                if ('Vt' not in file):
                                    folder = os.path.join(path, 'data_for_' + name)
                                else:
                                    folder = os.path.join(path, 'Vt_for_' + name)

                                if not os.path.exists(folder):
                                    os.makedirs(folder)
                                shutil.move(os.path.join(path, file), folder)

                    
                    for block in [28, 68, 48, 88, 38, 78, 18, 58, 98, 19, 59, 99, 118, 198]:
                        if 'block_num' + '_' + str(block) + '_' in file:
                            if 'dr_' in file:
                                key_wd = file.split('dr_')[1].split('_slice_')[0]
                            elif 'rdc_' in file:
                                key_wd = file.split('rdc_')[1].split('_slice_')[0]
                            else:
                                key_wd = ''

                            if block in [18, 58, 98, 19, 59, 99, 38, 78, 118, 198]:
                                name = '75C_DR'
                            else:
                                name = 0

                            if (('Vt' not in file) and name):
                                folder = os.path.join(path, 'data_for_' + name)
                            elif name:
                                folder = os.path.join(path, 'Vt_for_' + name)

                            if not os.path.exists(folder):
                                os.makedirs(folder)
                            
                            if name:
                                shutil.move(os.path.join(path, file), folder)
                    
                bar()
        # ***'''


# Merge All Vt Files together
folder = os.path.basename(base_path)
if 'Vt' in folder:
    print(folder)

    if 'WDC' in device or 'BiCS' in device or 'YMTC' in device or 'X29060' in device:
        wordline = 0
        key_word = 'FirstBlk_'
        wordline_list = [0, 6, 378, 390, 516, 524, 552, 654, 660]
        pattern_dict = {'plane':  0  } 
        if old_version:
            pattern_dict['block'] = r"(10|15|20)"
            # pattern_dict['block'] = r"(10|50|30|70)"
        elif old_version==False:
            pattern_dict['block'] = r"(10|90|170|15|95|175|20|100|180|25|105|185|30|110|190|35|115|195|40|120|200|45|125|205|50|130|210|55|135|215|60|140|220|65|145|225|38)"
            # pattern_dict['block'] = r"(10|50|90|170|15|55|95|175|20|60|100|180|25|65|105|185|30|70|110|190|35|75|115|195|40|80|120|200|45|85|125|205|50|130|210|55|135|215|60|140|220|65|145|225|38|118|198)"
            # pattern_dict['block'] = r"(10|50|90|130|15|55|95|20|60|25|30)"
        else:
            pattern_dict['block'] = r"(483|484|485|486|487)"
            # pattern_dict['block'] = r"(443|483|445|446|447)"

        folder_list, data_lst, wordline_col = [], [], []
        for folder_1st in os.listdir(base_path):
            if 'vtt_for' in folder_1st:
                suffix_name = folder_1st.split('_', 2)[-1]
                path = os.path.join(base_path, folder_1st)
                print('Process for folder:', '"' + folder_1st + '"')
                
                if make_subfolder:
                    for folder in os.listdir(path):
                        if 'Vtfor' in folder:
                            df_Vt = pd.DataFrame()
                            folder_path = os.path.join(path, folder)
                            print('            sub-folder:', '"' + folder + '"')
                            wl_list, lst = Vt.Vth_Call(folder_path, wordline_list=wordline_list, pattern_dict = pattern_dict, old_version = old_version)
                            data_lst.extend(lst)
                            wordline_col.extend(wl_list)
                else:
                    folder_path = path
                    print('        sub-folder:', '"' + folder_1st + '"')
                    wl_list, lst = Vt.Vth_Call(folder_path, wordline_list=wordline_list, pattern_dict = pattern_dict, old_version = old_version)
                    data_lst.extend(lst)
                    wordline_col.extend(wl_list)
                    
                df = pd.DataFrame(data_lst).T
                df.columns = wordline_col
                df.dropna(axis=1)
                df.drop(df.index[[(len(df)-1)]], inplace=True)
                
                if os.path.exists(os.path.join(os.path.dirname(folder_path), 'backup')):
                    shutil.rmtree(os.path.join(os.path.dirname(folder_path), 'backup'))
                os.makedirs(os.path.join(os.path.dirname(folder_path), 'backup'))

                timestamp = datetime.now().strftime('%Y%m%d_%H-%M-%S')
                if 'plane' in pattern_dict:
                    df.to_excel(os.path.join(os.path.dirname(folder_path), 'backup') + "\\" + timestamp + "_" + suffix_name + "_Vt_" + key_word + "Plane" + str(pattern_dict['plane']) + ".xlsx")
                    sub_name = "Vt_" + key_word + "Plane"  + str(pattern_dict['plane']) + "_byWL.xlsx"
                else:
                    df.to_excel(os.path.join(os.path.dirname(folder_path), 'backup') + "\\" + timestamp + "_" + suffix_name + "_Vt_" + key_word + "AllPlane" + ".xlsx")
                    sub_name = "Vt_" + key_word + "AllPlane" + "_byWL.xlsx"
                
                writer = pd.ExcelWriter(os.path.join(os.path.dirname(folder_path), 'backup') + '\\' + suffix_name + "_" + sub_name)

                for wl in wordline_list:
                    if (True in df.columns.str.contains('WL'+str(wl)+'_')) and (True in df.columns.str.contains('WL'+str(wl)+'_')):
                        df_wl = df.loc[:, df.columns.str.contains('WL'+str(wl)+'_')]
                        df_wl.to_excel(writer, sheet_name=str('WL'+str(wl)), index=False)
                writer.close()

    if 1:
        xlxs_list = []
        if make_subfolder:
            for folder in os.listdir(base_path):
                if 'vtt_for' in folder:
                    file_path = os.path.join(base_path, folder)         
        else:
            file_path = base_path

        for file in os.listdir(os.path.join(file_path, 'backup')):
            if sub_name in file:
                xlxs_list.append(os.path.join(file_path, 'backup', file))

        print('xlxs_list:', xlxs_list)

        workbook = xlwt.Workbook(encoding='ascii')
        # col = 1
        # m = 0
        sheet_dict = defaultdict(list)
        col_dict = defaultdict(list)
        for name in xlxs_list:
            wb = xlrd.open_workbook(name)
            sheets = wb.sheet_names()
            #按工作簿定位工作表
            for sheet in sheets:
                sh = wb.sheet_by_name(sheet)
                if sheet not in sheet_dict:
                    worksheet = workbook.add_sheet(sheet)
                    col_dict[sheet] = 1
                    # m = m + 1
                else:
                    worksheet = workbook.get_sheet(sheet)
                #遍历excel，打印所有数据
                for i in range(0, sh.nrows):
                    k = sh.row_values(i)
                    if (i == 0) and (sheet not in sheet_dict):
                        worksheet.write(i, 0, label=str('Num'))
                    elif (sheet not in sheet_dict):
                        worksheet.write(i, 0, label=int(i-1))
                    # 遍历每一行中的每一列
                    for j in range(0, len(k)):
                        if i == 0:
                            worksheet.write(i, col_dict[sheet]+j, label=str(k[j]))
                        else:
                            worksheet.write(i, col_dict[sheet]+j, label=int(k[j]))
                if sheet not in sheet_dict:
                    sheet_dict[sheet] = sheet
                # if sheet in col_dict:
                col_dict[sheet] = col_dict[sheet] + len(k)
            # col = col + len(k)
        timestamp = datetime.now().strftime('%Y%m%d_%H_%M_%S')
        workbook.save(os.path.join(base_path, timestamp + "_Vt_" + key_word + "Plane" + str(pattern_dict['plane']) + '_byWL.xls'))






    
