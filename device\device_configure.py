import sys
import re
from variable import globalvar as gl

device = gl.get_value('device').upper()
platform = gl.get_value('platform')

if platform =='Drive':
    if 'WDC'.upper() in device or 'BiCS4'.upper() in device:
        plane = 2
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 4
        blk_freq = 30
        rrt_entries = 33 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 5
        test_base_blk = {'0K PEC': 2, '3K PEC': 4, '7K PEC': 6, '10K PEC': 8} # for partially test
        test_partial_prog_ratio = {'Die0': ['10%', '25%', '50%', '75%', '100%']}
        test_partial_prog_wl = {'10%': 68, '25%': 96, '50%': 192, '75%': 288, '100%': 384} # the last wordline be programed = partial_prog_wl[x]-1
    
    elif 'BiCS5'.upper() in device: # Drive-645
        plane = 2
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 4
        blk_freq = 125
        rrt_entries = 33 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 2
        test_base_blk = {'0K PEC': 1, '3K PEC': 2, '7K PEC': 3, '10K PEC': 4} # for partially test
        test_partial_prog_ratio = {'Die0': ['25%', '50%', '100%'],
                                   'Die16':['25%', '75%', '100%']
                                   }
        
        test_partial_prog_wl = {'25%': 112, '50%': 220, '75%': 336, '100%': 448} # the last wordline be programed = partial_prog_wl[x]-1
        test_rd_count_set = [25*(10**6), 20*(10**6), 20*(10**6), 20*(10**6)]

    elif 'BiCS6'.upper() in device: # Drive-645
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 5
        blk_freq = 160
        rrt_entries = 51 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 5
        test_base_blk = {'0K PEC': 1, '3K PEC': 2, '7K PEC': 3, '10K PEC': 4} # for partially test
        test_partial_prog_ratio = {'Die0': ['25%', '50%', '100%'],
                                   'Die16':['25%', '75%', '100%']
                                   }
        test_partial_prog_wl = {'25%': 112, '50%': 220, '75%': 336, '100%': 448} # the last wordline be programed = partial_prog_wl[x]-1
        test_rd_count_set = [25*(10**6), 20*(10**6), 20*(10**6), 20*(10**6)]

    elif 'B47R'.upper() in device or 'Micron'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 4
        blk_freq = 40
        rrt_entries = 11
        codeword = 4588

        test_num_blk_per_pec = 5 # Eric: 5 -> 1
        test_base_blk = {'0K PEC': 4, '3K PEC': 8, '7K PEC': 12, '10K PEC': 16} # for partially test
        test_partial_prog_ratio = {'Die0': ['10%', '25%', '50%', '75%', '100%']
                                   }
        test_partial_prog_wl = {'10%': 72, '25%': 180, '50%': 352, '75%': 536, '100%': 711} # the last wordline be programed = partial_prog_wl[x]-1

    elif 'YMTC'.upper() in device or 'X29060'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 6
        blk_freq = 40
        rrt_entries = 28 # 29 -> 30 entries due to entry RR0 with all-0
        codeword = 4608

        test_num_blk_per_pec = 6
        test_base_blk = {'0K PEC': [6, 110]} # for partially test
        test_partial_prog_ratio = { 'All':  ['10%', '25%', '50%', '75%', '90%', '100%'],
                                    # 'Die0':  ['10%', '25%', '50%', '75%', '90%', '100%'],
                                   }
        test_partial_prog_wl = {'10%': 71, '25%': 186, '50%': 378, '75%': 571, '90%': 687, '100%': 768} # the last wordline be programed = partial_prog_wl[x]-1


elif platform =='NplusT':

    if 'B47R'.upper() in device or 'Micron'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 4
        blk_freq = 40
        rrt_entries = 8
        codeword = 4588

        test_num_blk_per_pec = 5
        test_block = ['0-3', '4-7', '8-11', '12-15']
        test_base_blk = {'0K PEC': 4, '3K PEC': 8, '7K PEC': 12, '10K PEC': 16} # for partially test
        test_partial_prog_ratio = {'Die0': ['10%', '25%', '50%', '75%', '100%']
                                   }
        test_partial_prog_wl = {'10%': 72, '25%': 180, '50%': 352, '75%': 536, '100%': 711} # the last wordline be programed = partial_prog_wl[x]-1


    elif 'YMTC'.upper() in device or 'X29060'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 6
        blk_freq = 40
        rrt_entries = 30 # 29 to 30 entries due to all-0 entry
        codeword = 4608
        open_rrt_entries = 28

        test_num_blk_per_pec = 2
        test_block = ['36-37', '24-25', '28-29', '32-33'] # 0K, 3K, 7K, 10K
        test_base_blk = {'0K PEC': 36, '3K PEC': 24, '7K PEC': 28, '10K PEC': 32} # for partially test
        test_partial_prog_ratio = {'0_0_0': ['25%', '50%'],
                                   '0_0_1': ['75%', '100%']
                                   }
        test_partial_prog_wl = {'25%': 192, '50%': 378, '75%': 570, '100%': 768} # the last wordline be programed = partial_prog_wl[x]-1
        
        
        # test_base_blk = [36, 24, 28, 32]
        # test_partial_prog_wl = [192, 384] # the last wordline be programed = partial_prog_wl[x]-1
        # test_partial_prog_ratio = ['25%', '50%']
        test_rd_count_set = [800000, 600000, 310000, 150000]

    elif 'X49060'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 10
        blk_freq = 440
        rrt_entries = 297 # 29 to 30 entries due to all-0 entry
        codeword = 4608
        open_rrt_entries = 38

        test_num_blk_per_pec = 2
        test_block = "[16-19][20-23][32-35][40-43]" # 0K, 3K, 7K, 10K 
        test_base_blk = {'0K PEC': 16, '3K PEC': 20, '7K PEC': 32, '10K PEC': 40} # for partially test
        test_partial_prog_ratio = {'All':  ['10%','25%']
                                   }
        test_partial_prog_wl = {'10%':195,'25%':395} # the last wordline be programed = partial_prog_wl[x]-1
                                # '10%': 160, '25%': 402, '50%': 783, '75%': 1204, '100%':1599
        
        
        # test_base_blk = [36, 24, 28, 32]
        # test_partial_prog_wl = [192, 384] # the last wordline be programed = partial_prog_wl[x]-1
        # test_partial_prog_ratio = ['25%', '50%']
        test_rd_count_set = [800000, 600000, 310000, 150000]    
        
    elif 'X36070' in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 4
        wl_per_layer = 6
        blk_freq = 380
        rrt_entries = 117 # 29 to 30 entries due to all-0 entry
        codeword = 4704
        open_rrt_entries = 38

        test_num_blk_per_pec = 5
        test_block = "[20-23][28-31][36-39][44-47][52-55][64-67]" # 0K, 1K, 2K, 3K, 4K, 5K
        test_base_blk = {'0K PEC': 20, '1K PEC': 28, '2K PEC': 36, '3K PEC': 44, '4K PEC': 52, '5K PEC': 64} # for partially test
        test_partial_prog_ratio = {'0_0_0':  ['10%','30%','50%','60%'],
                                   '1_0_0':  ['80%','97%','99%','100%']
                                   }
        test_partial_prog_wl = {'10%': 141, '30%': 424, '50%': 704, '60%': 847, '80%': 1132, '97%': 1376, '99%': 1394, '100%': 1415} # the last wordline be programed = partial_prog_wl[x]-1
                                # '10%': 160, '25%': 402, '50%': 783, '75%': 1204, '100%':1599
        
        
        # test_base_blk = [36, 24, 28, 32]
        # test_partial_prog_wl = [192, 384] # the last wordline be programed = partial_prog_wl[x]-1
        # test_partial_prog_ratio = ['25%', '50%']
        test_rd_count_set = [800000, 600000, 310000, 150000] 
        
    elif 'BiCS5'.upper() in device:
        plane = 2
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 4
        blk_freq = 800
        rrt_entries = 33 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 5
        test_block = "[20-21][30-31][36-37][44-45]" # for Drive didn't case, NplusT need
        test_base_blk = {'0K PEC': 20, '3K PEC': 30, '7K PEC': 36, '10K PEC': 44} # for partially test
        test_partial_prog_ratio = {'All': ['10%', '25%', '50%', '75%', '100%']
                                   } # 'CHP_CE_LUN' = '0_0_0'
        test_partial_prog_wl = {'10%': 44, '25%': 113, '50%': 217, '75%': 338, '100%': 447} # the last wordline be programed = partial_prog_wl[x]-1

    elif 'BiCS6'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 5
        blk_freq = 160
        rrt_entries = 51 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 5    # hang, 5 -> 1 for BlkRD
        
        if 0:
            ### Ea=0.64eV NAND
            test_block = "[32-35][64-67][96-99][112-115]" # Ea=0.64eV NAND
            test_base_blk = {'0K PEC': 32, '3K PEC': 64, '7K PEC': 96, '10K PEC': 112} # for partially test
        else:
            ### Ea=1.0eV NAND
            test_block = "[28-31][104-107][124-127][148-151]" # Ea=1.0eV NAND
            test_base_blk = {'0K PEC': 28, '3K PEC': 104, '7K PEC': 124, '10K PEC': 148} # for partially test

        test_partial_prog_ratio = {'All': ['10%', '25%', '50%', '75%', '100%']
                                   } # 'CHP_CE_LUN' = '0_0_0'    #'10%', '25%', '50%', '75%', '100%'    #'3%', '27%', '50%', '53%', '89%'
        test_partial_prog_wl = {'10%': 80, '25%': 202, '50%': 399, '75%': 607, '100%':809} # '10%': 80, '25%': 202, '50%': 404, '75%': 607, '100%':809 #'3%': 24, '27%': 219, '50%': 399, '53%': 429, '89%':724
                
        # test_base_blk = [2, 4, 6, 8] # for partially test
        # test_partial_prog_wl = [52, 112, 224, 336, 448] # the last wordline be programed = partial_prog_wl[x]-1
        # test_partial_prog_ratio = ['10%', '25%', '50%', '75%', '100%']

    elif 'BiCS8'.upper() in device:
        plane = 4
        Loffset = 4
        bits_per_cell = 3
        wl_per_layer = 5
        blk_freq = 400
        rrt_entries = 61 # 32 to 33 entries due to all-0 entry
        codeword = 4584

        test_num_blk_per_pec = 5    # hang, 5 -> 1 for BlkRD
        
        if 0:
            ### Ea=0.64eV NAND
            test_block = "[12-15][72-75][80-83][92-95]" # Ea=0.64eV NAND
            test_base_blk = {'0K PEC': 12, '3K PEC': 72, '7K PEC': 80, '10K PEC': 92} # for partially test
        else:
            ### Ea=1.0eV NAND
            test_block = "[20-23][32-35][40-43][48-51]" # Ea=1.0eV NAND
            test_base_blk = {'0K PEC': 20, '3K PEC': 32, '7K PEC': 40, '10K PEC': 48} # for partially test

        test_partial_prog_ratio = {'All': ['10%', '25%', '50%', '75%', '89%']} \
            # 'CHP_CE_LUN' = '0_0_0'   
            # '10%', '25%', '50%', '75%', '100%' 
            # '10%', '25%', '46%', '65%', '89%'    
            # '3%', '27%', '50%', '53%', '89%'
        test_partial_prog_wl = {'10%': 93, '25%': 213, '50%': 408, '75%': 618, '89%': 723} 
        # '10%': 118, '25%': 282, '46%': 513, '65%': 723, '89%': 988
        # '10%': 80, '25%': 202, '50%': 404, '75%': 607, '100%':809 
        # '3%': 24, '27%': 219, '50%': 399, '53%': 429, '89%':724
        # '10%': 108, '25%': 271, '46%': 503, '65%': 712, '89%': 977
        # '10%': 108, '25%': 271, '50%': 537, '75%': 816, '100%':1089
                
        # test_base_blk = [2, 4, 6, 8] # for partially test
        # test_partial_prog_wl = [52, 112, 224, 336, 448] # the last wordline be programed = partial_prog_wl[x]-1
        # test_partial_prog_ratio = ['10%', '25%', '50%', '75%', '100%']


